import datetime
import math

from sqlalchemy import and_, select, func
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import selectinload

import db.pg_orm as pg_orm
from db.order_utils import map_woo_order_status
from db.pg_client import PG_DB_PRODUCT
from db.pg_helps import object_as_dict
from db.pg_orm import Order
from db.pg_site_info import get_site_name
from db.postgres_async_client import PG_DB_ASYNC_PRODUCT_INS
from ecommerce.order.model import ListOrderInput, SubOrderItemOutput
from ecommerce.order.model import OrderItem, OrderLineItem, AIOrderStatus
from ecommerce.order.model import ParcelStatus, FulfillmentOrderStatus, ShopOrder, ShopShipping, FulfillmentInfo, \
    TimeInfo, ShopLineItem, PriceInfo, WooOrderStatusState
from utility.log import get_logger

log = get_logger(__name__)


def make_time(date_str):
    if date_str:
        return datetime.datetime.fromisoformat(date_str.replace("Z", "+00:00"))


def get_today_date_string(format_str="%Y-%m-%dT%H:%M:%SZ"):
    """
    Returns today's date as a string in the specified format.

    :param format_str: String format for the date (default is YYYY-MM-DD)
    :return: Today's date as a string
    """
    return datetime.datetime.now().strftime(format_str)


def make_order(order: pg_orm.Order) -> dict:
    def make_shipping(shipping_item):
        res = ShopShipping(
            name=shipping_item.get("name", ""),
            address=shipping_item["street"],
            phone=shipping_item["phone"],
        )
        return res

    def make_fulfillment():
        return FulfillmentInfo(
            fulfillment_method="import_shipment",
            package_number=""
        )

    def make_order_time(time):
        return TimeInfo(
            order_created=time if time else "",
            latest_delivery_time=time if time else "",
            delivery_time="",
            estimated_delivery_time=""
        )

    def make_products(product_items: list):
        res = [
            ShopLineItem(
                id=str(item["id"]),
                sku=item["sku"].upper() if item["sku"] else "",
                price=item["price"],
                quantity=item["quantity"],
                image=item["image"] if item["image"] else "",
                attr="",
                warehouse=""
            )
            for item in product_items
        ]
        return res

    def make_price_info(ai_order):
        price_info = PriceInfo(
            total_price=ai_order.total_price,
            total_discount=ai_order.total_discount,
            total_tax=ai_order.total_tax,
            total_shipping=ai_order.total_shipping,
            currency=ai_order.currency
        )
        return price_info

    return ShopOrder(
        order_id=order.order_id,
        site_id=order.site_id,
        org_id=order.org_id,
        name=order.name,
        order_status=order.order_status,
        shipping=make_shipping(order.shipping),
        products=make_products(order.products),
        time_info=make_order_time(order.date_paid),
        fulfillment_info=make_fulfillment(),
        price_info=make_price_info(order)
    ).model_dump()


def order_to_dict(order: pg_orm.Order):
    dic = {
        "order_id": order.order_id,
        "status": order.order_status,
        "update_time": str(order.sync_time),
        "name": order.name,
    }
    return dic


def set_raw_order_status(order_id: str, status):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        raw_order = pg_session.query(
            pg_orm.Order
        ).filter_by(order_id=order_id).first()
        if raw_order:
            raw_order.status = status
            pg_session.add(raw_order)
            pg_session.commit()
            return raw_order


# def create_fulfillment_order(order_id, fulfillment_order_id, line_items, status, tracking_number) -> bool:
#     with PG_DB_PRODUCT.get_db_session() as pg_session:
#         fulfillment_order = pg_orm.FulfillmentOrder(
#             order_id=order_id,
#             fulfillment_id=fulfillment_order_id,
#             # line_items=line_items,
#             status=status,
#             # tracking_number=tracking_number
#         )
#         pg_session.add(fulfillment_order)
#         pg_session.commit()
#         return True


def set_fulfillment_order_status(fulfillment_order_id: str, status) -> bool:
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        raw_order = pg_session.query(
            pg_orm.FulfillmentOrder
        ).filter_by(fulfillment_id=fulfillment_order_id).first()
        if raw_order:
            raw_order.status = status
            pg_session.add(raw_order)
            pg_session.commit()
            return True


def set_order_status(order_id: str, line_items, status, tracking_number):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        raw_order = pg_session.query(
            pg_orm.Order
        ).filter_by(order_id=order_id).first()
        if raw_order:
            raw_order.status = status
            pg_session.add(raw_order)
            pg_session.commit()

        order = pg_session.query(
            pg_orm.FulfillmentOrder
        ).filter_by(order_id=order_id).first()
        log.info(f"order is {order}")
        if order:
            order.status = status
            order.line_items = line_items
            order.tracking_number = tracking_number
            pg_session.add(order)
            pg_session.commit()
            return f"set order {order_id} status successful"


def update_order_data(order_ids, site_id, org_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        for order_item in order_ids:
            order: Order = pg_session.query(Order).filter_by(
                order_id=order_item["id"]
            ).first()
            if order:
                order.update_time = datetime.datetime.now()
                order.sync_time = datetime.datetime.now()
            else:
                order = Order(
                    order_id=order_item["id"],
                    site_id=site_id,
                    org_id=org_id,
                    name=order_item["name"],
                    sync_time=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    order_status="created"
                )
                pg_session.add(order)
            pg_session.commit()


def list_order_data(site_connection_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        orders = pg_session.query(
            pg_orm.Order
        ).filter_by(site_id=site_connection_id).all()
        if orders:
            return [order_to_dict(order) for order in orders]


def update_fulfillment_orders(order_data):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        for fulfillment_order in order_data:
            data = pg_orm.FulfillmentOrder(
                # tracking_number="",
                status=fulfillment_order["status"],
                # line_items=[],
                fulfillment_id=fulfillment_order["fulfillment_order_id"],
                order_id=fulfillment_order["order_id"]
            )
            pg_session.add(data)
        pg_session.commit()


def find_site_id_by_order(order_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order: Order = pg_session.query(
            pg_orm.Order
        ).filter_by(order_id=order_id).first()
        if order:
            return order.site_id


def list_fulfillment_orders(page, limit, order_id=None):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        total_count = pg_session.query(pg_orm.FulfillmentOrder).count()
        total_pages = math.ceil(total_count / limit)
        param = {}
        if order_id:
            param["order_id"] = order_id
        # if tracking_number:
        # param["tracking_number"] = tracking_number
        if param:
            orders = pg_session.query(pg_orm.FulfillmentOrder).filter_by(**param). \
                filter(pg_orm.FulfillmentOrder.status != "cancelled"). \
                limit(limit).offset((page - 1) * limit).all()
        else:
            orders = pg_session.query(pg_orm.FulfillmentOrder) \
                .limit(limit) \
                .offset((page - 1) * limit) \
                .all()
        if orders:
            return [{
                "id": order.id,
                "fulfillment_id": order.fulfillment_id,
                "order_id": order.order_id,
                "status": order.status,
                "products": order.products,
                "packages": order.packages,
                "created_time": str(order.created_time),
                "order_type": order.order_type,
                "platform_type": order.platform_type,
                "shipping": order.shipping,
                "site_id": order.site_id,
            } for order in orders], total_count, total_pages
        else:
            return [], total_count, total_pages


def append_orders(orders, site_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        try:
            old_orders_ids = pg_session.query(pg_orm.Order.order_id).filter_by(site_id=site_id).all()
            order_id_list = [_id[0] for _id in old_orders_ids]

            new_orders = []
            for new_order in orders:
                new_order_id = str(new_order.order_id)
                # log.info(f"new order id is {new_order_id}")
                if new_order_id not in order_id_list:
                    new_orders.append(new_order)
                else:
                    log.info(f"Order {new_order_id} already has an order")

            pg_session.bulk_save_objects(new_orders)
            pg_session.commit()
        except Exception as e:
            log.info(f"can't reset orders, the reason is {e}")
            raise Exception()


def append_order(order, site_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        try:
            old_orders_ids = pg_session.query(pg_orm.Order.order_id).filter_by(site_id=site_id).all()
            order_id_list = [_id[0] for _id in old_orders_ids]
            new_order_id = str(order.order_id)
            status = order.order_status
            if new_order_id not in order_id_list:
                pg_session.add(order)
                pg_session.commit()
                log.info(f"create Order {new_order_id}")
            else:
                order = pg_session.query(pg_orm.Order).filter(pg_orm.Order.order_id == new_order_id).first()
                order.order_status = status
                pg_session.add(order)
                pg_session.commit()
                log.info(f"update Order {new_order_id}")

        except Exception as e:
            log.info(f"can't reset orders, the reason is {e}")
            raise Exception()


def get_order_data(site_name, pg_order: pg_orm.Order, sub_orders) -> OrderItem:
    """
    Transform SQLAlchemy Order model to Pydantic OrderItem model

    Args:

    Returns:
        OrderItem: Transformed Pydantic OrderItem model
    """

    # Create a SubOrderItem with all the inherited fields from OrderItem

    def make_sub_order(order_item, sub_order):
        order_shipping_info = order_item.shipping or {}
        order_receiver = order_shipping_info.get("last_name", "") + " " + order_shipping_info.get("first_name", "")
        sub_order = SubOrderItemOutput(
            id=sub_order.id,
            order_type="sub_order",
            order_name=sub_order.sub_order_number,
            site_name=site_name,
            country=order_shipping_info.get("country"),
            order_status=sub_order.status,
            line_items=get_order_lines(sub_order),
            receiver=order_receiver,
            contact=order_shipping_info.get("phone") or order_shipping_info.get("email"),
            shipping_address=order_shipping_info.get("address_1") or order_shipping_info.get("address_2"),
            created_time=sub_order.created_time,
            latest_delivery_time=None,
            delivery_time=None,
            estimated_time=None,
            tracking_number=shipping_info.get("tracking_number"),
            delivery_method=shipping_info.get("method"),
            # Add SubOrderItem specific fields
            sub_order_number=sub_order.sub_order_number,
            warehouse=sub_order.warehouse,
            supplier_info=sub_order.supplier_info
        )

        return sub_order

    def make_attributes(attributes: list):
        log.info(f"attributes are {attributes}")
        # [{'id': 392, 'key': 'pa_color', 'value': 'bluette', 'display_key': 'Color', 'display_value': 'Bluette'}}]
        result = []
        for attribute in attributes:
            key = attribute.get("display_key").lower() if attribute.get("display_key") else None
            value = attribute.get("display_value").lower() if attribute.get("display_value") else None
            if key in ["color", "size"] and value:
                result.append({"key": key, "value": value})
        log.info(f"attributes for ui are {result}")
        return result

    def make_symbol():
        symbol = ""
        if pg_order.currency == "EUR":
            symbol = "€"
        if pg_order.currency == "USD":
            symbol = "$"
        return f"{pg_order.currency} {symbol}"

    # Transform line items
    transformed_line_items = []
    for line_item in pg_order.line_items:
        transformed_line_items.append(OrderLineItem(
            name=line_item.name,
            image=line_item.image,
            product_id=line_item.product_id,
            variant_id=line_item.variant_id,
            wd_variant_id=line_item.wd_variant_id,
            wd_product_id=line_item.wd_product_id,
            spu=line_item.spu,
            sku=line_item.sku,
            attributes=make_attributes(line_item.attributes),  # Ensure attributes is a string
            warehouse=line_item.warehouse if line_item.warehouse else "",
            price=f"{make_symbol()}{line_item.price}",
            quantity=line_item.quantity
        ))

    # Extract shipping information (assuming shipping is a JSON field)
    shipping_info = pg_order.shipping or {}
    receiver = shipping_info.get("last_name", "") + " " + shipping_info.get("first_name", "")
    if not transformed_line_items:
        order = OrderItem(
            order_type="order",
            id=pg_order.id,
            is_sync=pg_order.is_synchronized or False,
            order_name=pg_order.order_id,
            site_name=site_name,
            country=shipping_info.get("country"),
            order_status=pg_order.order_status,
            line_items=transformed_line_items,
            receiver=receiver,
            contact=shipping_info.get("phone") or shipping_info.get("email"),
            shipping_address=shipping_info.get("address_1") or shipping_info.get("address_2"),
            created_time=pg_order.created_time.strftime("%Y-%m-%dT%H:%M:%SZ") if pg_order.created_time else None,
            latest_delivery_time=pg_order.latest_delivery_time.strftime("%Y-%m-%dT%H:%M:%SZ")
            if pg_order.latest_delivery_time else None,
            delivery_time=None,  # You may need to add logic to populate this
            estimated_time=None,  # You may need to add logic to populate this
            delivery_method=shipping_info.get("method"),
            tracking_number=shipping_info.get("tracking_number"),
            sub_orders=None
        )
        return order
    else:
        sub_orders = [make_sub_order(pg_order, sub_order)
                      for sub_order in sub_orders]
        order = OrderItem(
            order_type="order",
            id=pg_order.id,
            is_sync=pg_order.is_synchronized or False,
            order_name=pg_order.order_id,
            site_name=site_name,
            country=shipping_info.get("country"),
            order_status=pg_order.order_status,
            line_items=transformed_line_items,
            receiver=receiver,
            contact=shipping_info.get("phone") or shipping_info.get("email"),
            shipping_address=shipping_info.get("address_1") or shipping_info.get("address_2"),
            created_time=pg_order.created_time.strftime("%Y-%m-%dT%H:%M:%SZ") if pg_order.created_time else None,
            latest_delivery_time=pg_order.latest_delivery_time.strftime("%Y-%m-%dT%H:%M:%SZ")
            if pg_order.latest_delivery_time else None,
            delivery_time=None,  # You may need to add logic to populate this
            estimated_time=None,  # You may need to add logic to populate this
            delivery_method=shipping_info.get("method"),
            tracking_number=shipping_info.get("tracking_number"),
            sub_orders=sub_orders)
        return order


def check_order_status(order_id, site_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.Order).filter_by(id=order_id).filter_by(site_id=site_id).first()
        if order:
            if order.order_status == AIOrderStatus.CONFIRMED.value:
                return True
            else:
                return False
        else:
            return False


def check_sible_sub_order_status(sub_order):
    """
    Check if all sibling sub-orders (including the given sub-order) have the same status.

    Args:
        sub_order (pg_orm.SubOrder): The sub-order to check against its siblings

    Returns:
        bool: True if all sibling sub-orders have the same status, False otherwise
    """
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        # Get parent order's all sub orders
        parent_order = sub_order.parent_order
        all_sub_orders = pg_session.query(pg_orm.SubOrder).filter_by(
            main_order_id=parent_order.id
        ).all()

        # Get the status of the current sub_order
        current_status = sub_order.status

        # Check if all sub orders have the same status
        for sibling_order in all_sub_orders:
            if sibling_order.status != current_status:
                return False

        return True


def set_ai_order_status(order_id, order_status):
    """
    Update the status of an order or sub-order in the database.
    For sub-orders, it also syncs the parent order status if all sibling sub-orders
    have the same status.

    Args:
        order_id (int): The ID of the order or sub-order to update
        order_status (str): The new status to set

    Returns:
        bool: True if the status was successfully updated, False if the order was not found

    Example:
        >>> set_ai_order_status(1000000001, 2)
        True
    """
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.Order).filter_by(id=order_id).first()
        sub_order = pg_session.query(pg_orm.SubOrder).filter_by(id=order_id).first()
        if sub_order:
            # sub order
            parent_order = sub_order.parent_order
            sub_order.status = order_status
            # sync order status if all sub orders status are the same state
            check_status = check_sible_sub_order_status(sub_order)
            if check_status:
                log.info("all sub orders status are the same state, update parent order status")
                parent_order.order_status = order_status
                pg_session.add(parent_order)
            pg_session.add(sub_order)
            pg_session.commit()
            return True
        else:
            order = pg_session.query(pg_orm.Order).filter_by(id=order_id).first()
            if order:
                order.order_status = order_status
                pg_session.commit()
                return True
            else:
                return False


def set_isc_order(order_id, isc_order_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.Order).filter_by(id=order_id).first()
        if order:
            order.meta_data["isc_order_id"] = isc_order_id
            pg_session.commit()


def get_isc_order(order_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.Order).filter_by(id=order_id).first()
        if order:
            if order.meta_data.get("isc_order_id"):
                return order.meta_data["isc_order_id"]


def get_sub_orders(order):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.Order).filter_by(id=order.id).first()
        sub_orders = order.sub_orders
        return sub_orders


def get_order_lines(sub_order):
    def fix_float(order):
        if order:
            result = object_as_dict(order)
            if result:
                result["quantity"] = round(result["quantity"], 0)
                return result

    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.SubOrder).options(
            selectinload(pg_orm.SubOrder.line_items)
        ).filter_by(id=sub_order.id).first()
        if order:
            sub_orders = [fix_float(order_line) for order_line in order.line_items]
            return sub_orders
        else:
            return []


async def get_ai_orders(site_id, order_input: ListOrderInput):
    site_name = get_site_name(site_id)
    try:
        async with PG_DB_ASYNC_PRODUCT_INS.async_session() as pg_session:
            # Using and_ for combined filter conditions
            query = select(Order).distinct().options(
                selectinload(Order.line_items)
            ).join(pg_orm.OrderLineItem, Order.id == pg_orm.OrderLineItem.order_id)

            # List to collect all filter conditions
            filters = [Order.site_id == site_id]
            if order_input.sku_id:
                filters.append(pg_orm.OrderLineItem.sku == order_input.sku_id)

            if order_input.order_status:
                filters.append(Order.order_status.in_(order_input.order_status))

            if order_input.order_number:
                filters.append(Order.order_id == order_input.order_number)

            # Handle time range filters
            if order_input.created_time_start:
                start_time = datetime.datetime.fromisoformat(order_input.created_time_start.replace("Z", "+00:00"))
                filters.append(Order.created_time >= start_time)

            if order_input.created_time_end:
                end_time = datetime.datetime.fromisoformat(order_input.created_time_end.replace("Z", "+00:00"))
                filters.append(Order.created_time <= end_time)

            if order_input.latest_delivery_time:
                filters.append(
                    datetime.datetime.
                    strptime(Order.delivery_time.astext, "%m/%d/%Y") == order_input.latest_delivery_time
                )

            # Apply all filters
            if filters:
                query = query.where(and_(*filters))

            count_query = select(func.count()).select_from(query.subquery())
            total_count = await pg_session.scalar(count_query)
            # Handle pagination
            if order_input.page and order_input.page_limit:
                offset = (order_input.page - 1) * order_input.page_limit
                query = query.offset(offset).limit(order_input.page_limit).order_by(Order.created_time.desc())

                result = await pg_session.execute(query)
                orders = [order[0] for order in result.all()]
                order_data = []
                for order in orders:
                    sub_orders = get_sub_orders(order)
                    order = get_order_data(site_name, order, sub_orders)
                    order_data.append(order)

                total_pages = math.ceil(total_count / order_input.page_limit)
                return order_data, total_count, total_pages
            return None

    except SQLAlchemyError as e:
        log.info(f"Database error while fetching orders: {str(e)}")
        raise
    except Exception as e:
        log.info(f"Unexpected error in fetching orders: {str(e)}")
        raise


def delete_ai_order(sync_order_ids):
    """
    TODO: delete orders and cancel the sync state
    """
    try:
        with PG_DB_PRODUCT.get_db_session() as pg_session:
            for order_id in sync_order_ids:
                order = pg_session.query(pg_orm.Order).filter_by(id=order_id).first()
                pg_session.delete(order)
                pg_session.commit()
        return True
    except Exception as e:
        log.info(f"error is {str(e)}")
        return False


def set_ai_order_sync_status(sync_order_ids, is_sync):
    try:
        with PG_DB_PRODUCT.get_db_session() as pg_session:
            for order_id in sync_order_ids:
                order = pg_session.query(pg_orm.Order).filter_by(id=order_id).first()
                order.is_sync = is_sync
                pg_session.commit()
        return True
    except Exception as e:
        log.info(f"error is {str(e)}")
        return False


def get_shop_order_ids(site_id, order_ids):
    try:
        with PG_DB_PRODUCT.get_db_session() as session:
            query = (
                session.query(pg_orm.Order.order_id)
                .filter_by(id.in_(order_ids))
                .filter_by(site_id == site_id)
            )
            result = query.all()
            if result:
                return [item[0] for item in result]
            return result

    except Exception as e:
        log.info(f"error is: {str(e)}")


def update_order_status(woo_order_id, updated_order, order_status: WooOrderStatusState):
    try:
        with PG_DB_PRODUCT.get_db_session() as session:
            query = (
                session.query(pg_orm.Order)
                .filter_by(order_id=str(woo_order_id))
            )
            result = query.first()
            if result:
                ai_order_status = AIOrderStatus(result.order_status)
                for key, value in updated_order.items():
                    if key == "status":
                        log.info(f"woo_order_status is {order_status}, ai_order_status is {ai_order_status}")
                        result.order_status = map_woo_order_status(order_status, ai_order_status)
                        log.info(f"new order status is "
                                 f"{result.order_status}")
                log.info(f"update order is {result}")
                session.commit()
                return True
            else:
                return False
    except Exception as e:
        log.info(f"error is: {str(e)}")
        return False


def get_order_by_id(order_id):
    with PG_DB_PRODUCT.get_db_session() as session:
        existing_order = session.query(pg_orm.Order).filter(
            Order.order_id == str(order_id)
        ).options(selectinload(pg_orm.Order.line_items)).first()
        if existing_order:
            return existing_order


def _sync_line_items(session, order_id, wc_line_items: list, _currency):
    """
    Sync line items for a given order

    :param session: SQLAlchemy database session
    :param wc_line_items: List of line items from WooCommerce
    """
    # Remove existing line items
    try:

        session.query(pg_orm.OrderLineItem).filter(pg_orm.OrderLineItem.order_id == order_id).delete()
        # Add new line items
        for item in wc_line_items:
            line_item = pg_orm.OrderLineItem(
                order_id=int(order_id),
                product_id=str(item.get("product_id", "")),
                variant_id=str(item.get("variation_id", "")),
                wd_variant_id=item.get("variation_id", ""),
                wd_product_id=item.get("product_id", ""),
                spu=str(item.get("product_id", "")),
                sku=item.get("sku", ""),
                name=item.get("name", ""),
                image=item.get("image").get("src") if item.get("image") else "",
                quantity=item.get("quantity", 0.),
                price=round(float(item.get("price")), 2),
                warehouse=None,  # You might want to add logic to determine warehouse
                attributes=item.get("meta_data", {})
            )
            session.add(line_item)
    except Exception as e:
        session.rollback()
        log.info(f"Error syncing WooCommerce order: {str(e)}")


def save_pg_orders(orders: list[Order]):
    with PG_DB_PRODUCT.get_db_session() as session:
        session.bulk_save_objects(orders)
        session.commit()


def save_orders(order_items: list[OrderItem]):
    # Convert Pydantic model to SQLAlchemy model
    for order_item in order_items:
        with PG_DB_PRODUCT.get_db_session() as session:
            if session.query(pg_orm.Order).filter_by(order_id=str(order_item.id)).first():
                log.info(f"skip order {order_item.id}")
                continue
            order = pg_orm.Order(
                order_id=str(order_item.id),  # Assuming order_id is the same as id
                sync_time=datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                org_id=1,  # Placeholder, replace with actual org_id
                order_status=order_item.order_status.value,
                user_id="user123",  # Placeholder, replace with actual user_id
                raw_order_status="raw_status",  # Placeholder
                shipping={},  # Placeholder
                products={},  # Placeholder
                total_price="0.0",  # Placeholder
                total_discount="0.0",  # Placeholder
                total_shipping="0.0",  # Placeholder
                total_tax="0.0",  # Placeholder
                payment_method="credit_card",  # Placeholder
                date_paid=order_item.created_time,
                currency="USD",  # Placeholder
                platform_type=1,  # Placeholder
                raw_products={},  # Placeholder
                site_id=1,  # Placeholder
                meta_data={},  # Placeholder
                created_time=order_item.created_time,
                latest_delivery_time=order_item.latest_delivery_time if order_item.latest_delivery_time else None,
                is_sync=True
            )

            session.add(order)
            session.flush()  # Flush to get the order.id

            for line_item in order_item.line_items:
                order_line_item = pg_orm.OrderLineItem(
                    order_id=order.id,
                    product_id=str(line_item.product_id),
                    variant_id=str(line_item.variant_id),
                    wd_variant_id=line_item.wd_variant_id,
                    wd_product_id=line_item.wd_product_id,
                    price=line_item.price,
                    quantity=line_item.quantity,
                    sku=line_item.sku,
                    spu=line_item.spu,
                    attributes=line_item.attributes,
                    warehouse=line_item.warehouse,
                    name=line_item.name,
                    image=line_item.image
                )
                session.add(order_line_item)

            session.commit()


def create_ai_order(order_data: dict, site_id, org_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        rest_data = {
            "site_id": site_id,
            "org_id": org_id,
            "order_status": "",
            "sync_time": str(datetime.datetime.now()).split(" ")[0],
        }
        data = order_data | rest_data
        order = pg_orm.Order(**data)
        pg_session.add(order)
        pg_session.commit()


def create_ai_fulfillment_order(order_data):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        fulfillment_order = pg_orm.FulfillmentOrder(
            **order_data
        )
        pg_session.add(fulfillment_order)
        pg_session.commit()
        return fulfillment_order.id


def get_fulfillment_order(order_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        fulfillment_order = pg_session.query(pg_orm.FulfillmentOrder).filter_by(order_id=order_id).first()
        if fulfillment_order:
            return object_as_dict(fulfillment_order)
        else:
            return


def get_fulfillment_order_data(fulfillment_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        fulfillment_order: pg_orm.FulfillmentOrder = pg_session.query(pg_orm.FulfillmentOrder).filter_by(
            fulfillment_id=fulfillment_id).first()
        if fulfillment_order:
            order_id = fulfillment_order.order_id
            line_items = fulfillment_order.products
            address_item = fulfillment_order.shipping
            platform_type = fulfillment_order.platform_type
            order_type = fulfillment_order.order_type
            return order_id, address_item, line_items, platform_type, order_type
        else:
            return


def merge_packages(packages, line_items, carrier_tracking_ref, status):
    """
    "line_items":[{"wd_variant_id":"wd_123", "quantity":2}],
    "carrier_tracking_ref":4PX9060005371452CN
    "status":sent
    "packages":[
    {"sku": "48704721879356","company":"4PX", "tracking_number":"4PX9060005371452CN", "quantity":2, "status":"sent"}]
    """
    new_packages = []
    for item in line_items:
        new_packages.append({
            "sku": item.get("wd_variant_id"),
            "company": "4PX",
            "tracking_number": carrier_tracking_ref,
            "quantity": item.get("quantity"),
            "status": status,
        })
    if packages:
        return packages + new_packages
    else:
        return new_packages


def update_packages(fulfillment_order_id, line_items, carrier_tracking_ref, status):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.FulfillmentOrder).filter_by(order_id=fulfillment_order_id).first()
        if order:
            order.packages = merge_packages(order.packages, line_items, carrier_tracking_ref, status)
            pg_session.add(order)
            pg_session.commit()


def set_direct_delivery_order_status(fulfillment_order_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.FulfillmentOrder).filter_by(id=fulfillment_order_id).first()
        if order:
            order.status = FulfillmentOrderStatus.completed.name
            pg_session.add(order)
            pg_session.commit()
            return True
        else:
            return False


def set_order_cancelled_status(order_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.FulfillmentOrder).filter_by(fulfillment_id=order_id).first()
        if order:
            order.status = "cancelled"
            pg_session.add(order)
            pg_session.commit()
            return True


def get_fulfillment_order_product(order_id):
    """
    [{'price': 22.32,
  'quantity': 2,
  'sku': 'WD_201_6821357_5187098678325',
  'name': '',
  'image': ''}]
    """
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.FulfillmentOrder).filter_by(fulfillment_id=order_id).first()
        if order:
            return order.products, order.order_id


def get_order_product(order_id) -> dict:
    """
    'id': '48704733872444',
    'name': "Jeans Women's Loose Straight Summer Thin Arrival High Waist ",
    'price': '22.32',
    'sku': 'WD_201_6821357_5187098678325',
    'quantity': 1,
    'image': '',
    'product_handle': 'https://woolworlds.myshopify.com/products/wd_201_6821357_719624792476'
    """
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.Order).filter_by(order_id=order_id).first()
        if order:
            return order.products


def get_order_raw_product(order_id) -> dict:
    """
    'id': '48704733872444',
    'name': "Jeans Women's Loose Straight Summer Thin Arrival High Waist',
    'price': '22.32',
    'sku': 'WD_201_6821357_5187098678325',
    'quantity': 1,
    'image': '',
    'product_handle': 'https://woolworlds.myshopify.com/products/wd_201_6821357_719624792476'
    """
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.Order).filter_by(order_id=order_id).first()
        if order:
            return order.raw_products


def get_products_from_order(order_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        orders = pg_session.query(pg_orm.FulfillmentOrder).filter_by(order_id=order_id,
                                                                     status=ParcelStatus.delivered).all()
        if orders:
            return [order.products for order in orders]


def set_order_packages_status(order_id, order_condition):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.Order).filter_by(order_id=order_id).first()
        if order:
            order.status = order_condition
            pg_session.add(order)
            pg_session.commit()


def set_order_paid_status(order_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.FulfillmentOrder).filter_by(fulfillment_id=order_id).first()
        if order:
            order.status = "paid"
            pg_session.add(order)
            pg_session.commit()
            return order.order_id


def set_raw_order_paid_status(order_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.Order).filter_by(order_id=order_id).first()
        if order:
            order.order_status = "paid"
            pg_session.add(order)
            pg_session.commit()
            return True


def get_order_products(order_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        fulfillment_order = pg_session.query(pg_orm.FulfillmentOrder).filter_by(id=order_id).first()
        if fulfillment_order:
            fulfillment_order.status = ParcelStatus.delivered.name
            pg_session.add(fulfillment_order)
            pg_session.commit()
            fulfillment_id = fulfillment_order.fulfillment_id
            fulfillment_products = fulfillment_order.products
            order_id = fulfillment_order.order_id
            if order_id:
                order = pg_session.query(pg_orm.Order).filter_by(order_id=order_id).first()
                order_products = order.products
                return fulfillment_id, fulfillment_products, order_products, order_id


def update_order_products(order_id, result):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.Order).filter_by(order_id=order_id).first()
        if order:
            order.products = result
            pg_session.add(order)
            pg_session.commit()
            log.info(f"update order products {result}")
        else:
            log.info(f"update order {order_id} products failed")


def get_order_id(order_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.Order).filter_by(order_id=order_id).first()
        if order:
            return order.id
        else:
            log.info(f"get order {order_id} failed")
            return


def check_unpaid_order(order_id):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.FulfillmentOrder).filter_by(order_id=order_id, status="unpaid").first()
        if order:
            log.info(f"FulfillmentOrder with order id {order_id} is unpaid")
            return True
        else:
            return False


def make_order_status(order_id, status):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.Order).filter_by(order_id=order_id).first()
        if order:
            log.info(f"set order {order_id} status {status}")
            order.order_status = status
            pg_session.add(order)
            pg_session.commit()
            return True
        else:
            return False


def make_fulfillment_order_status(order_id, status):
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        order = pg_session.query(pg_orm.FulfillmentOrder).filter_by(order_id=order_id).first()
        if order:
            log.info(f"set order {order_id} status {status}")
            order.status = status
            pg_session.add(order)
            pg_session.commit()
            return True
        else:
            return False


def get_fulfillment_products(order_id) -> list[dict]:
    with PG_DB_PRODUCT.get_db_session() as pg_session:
        completed_orders = pg_session.query(pg_orm.FulfillmentOrder). \
            filter_by(order_id=order_id, status=FulfillmentOrderStatus.completed).all()
        if completed_orders:
            result = []
            for order in completed_orders:
                result += order.products
            return result
        else:
            return []

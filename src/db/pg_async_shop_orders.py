from soupsieve.util import lower
from sqlalchemy import update, select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession

from db.pg_async_base_crud import AsyncBaseCRUD
from db.pg_orm import Order
from ecommerce.product.common_api import AIOrderStatus
from utility.log import get_logger

log = get_logger(__name__)


class ShopOrderCRUD(AsyncBaseCRUD[Order]):
    def __init__(self):
        super().__init__(Order)

    @staticmethod
    async def update_shop_order_status(pg_session: AsyncSession, order_id: str, order_status: int) -> Order | None:
        """
            更新商店订单状态
        """
        try:
            stmt = (
                update(Order)
                .where(Order.id == order_id)
                .values(order_status=order_status, order_status_raw=lower(AIOrderStatus(order_status).name))
                .returning(Order)
            )
            query = await pg_session.execute(stmt)
            await pg_session.flush()
            return query.scalar_one_or_none()
        except SQLAlchemyError as e:
            log.error(f"Failed to update shop order status order_id = {order_id}: {e}")
            return None

    @staticmethod
    async def get_shop_order_by_order_id(pg_session: AsyncSession, order_id: str) -> Order | None:
        try:
            stmt = select(Order).where(Order.order_id == order_id)
            result = await pg_session.execute(stmt)
            return result.scalars().first()
        except SQLAlchemyError as e:
            log.exception(e)
            return None


shop_order_crud = ShopOrderCRUD()

from db.auth_orm import MetadataERPSettingTable, MetadataLogSettingTable, MetadataServiceTable
from db.pg_helps import object_as_dict
from db.pg_client import PG_DB_SERVICE_INS
from db.pg_orm import MetadataAuthTable


def _get_metadata(records):
    log_settings = {}
    for record in records:
        rec = object_as_dict(record)
        log_settings[rec.get("key")] = rec.get("value")
    return log_settings


def get_metadata_log_setting():
    with PG_DB_SERVICE_INS.get_db_session() as pg_session:
        records = pg_session.query(MetadataLogSettingTable)
        return _get_metadata(records)


def get_metadata_erp_setting():
    with PG_DB_SERVICE_INS.get_db_session() as pg_session:
        records = pg_session.query(MetadataERPSettingTable)
        return _get_metadata(records)


def get_metadata_auth():
    with PG_DB_SERVICE_INS.get_db_session() as pg_session:
        records = pg_session.query(MetadataAuthTable)
        return _get_metadata(records)


def get_metadata_service_setting():
    with PG_DB_SERVICE_INS.get_db_session() as pg_session:
        records = pg_session.query(MetadataServiceTable)
        return _get_metadata(records)

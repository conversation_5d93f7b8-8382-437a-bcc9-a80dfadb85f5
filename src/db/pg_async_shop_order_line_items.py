from sqlalchemy import delete
from sqlalchemy.ext.asyncio import AsyncSession

from db.pg_async_base_crud import AsyncBaseCRUD
from db.pg_orm import OrderLineItem
from utility.log import get_logger

log = get_logger(__name__)


class ShopOrderLineItemsCRUD(AsyncBaseCRUD[OrderLineItem]):
    def __init__(self):
        super().__init__(OrderLineItem)

    @staticmethod
    async def delete_by_shop_order_id(pg_session: AsyncSession, order_id: int):
        await pg_session.execute(delete(OrderLineItem).where(OrderLineItem.order_id == order_id))


shop_order_line_items_crud = ShopOrderLineItemsCRUD()

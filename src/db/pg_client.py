import os
from urllib.parse import quote

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from db import pg_orm as postgres_orm
from utility.config import PG_SERVICE_DB_NAME, PG_AUTH_DB_NAME, PG_DB_NAME


class PostgresClient:
    _instances = {}

    @classmethod
    def get_instance(cls, pg_database):
        if pg_database not in cls._instances:
            cls._instances[pg_database] = cls(pg_database)
        return cls._instances[pg_database]

    def __init__(self, db_database=None):
        self.pg_host = os.getenv("PG_HOST")
        self.pg_port = os.getenv("PG_PORT", 5432)
        self.pg_user = os.getenv("PG_DB_USER")
        self.pg_password = os.getenv("PG_DB_PWD")
        self.pg_database = db_database

        # print('==========', self.pg_host, self.pg_port, self.pg_user, self.pg_password, self.pg_database)

        assert self.pg_host, "POSTGRES host cannot be empty!"
        assert self.pg_port, "POSTGRES port cannot be empty!"
        assert self.pg_user, "POSTGRES user cannot be empty!"
        assert self.pg_database, "POSTGRES database cannot be empty!"
        assert self.pg_password, "POSTGRES password cannot be empty!"

        # create_engine("数据库类型+数据库驱动://数据库用户名:数据库密码@IP地址:端口/数据库"
        # engine = create_engine("postgresql+psycopg2://user:pass@host/dbname?client_encoding=utf8")
        # Password contains sequence of special characters, so we quote it.
        self.db_url = f"postgresql+psycopg2://{self.pg_user}:{quote(self.pg_password)}@{self.pg_host}:{self.pg_port}/{self.pg_database}"
        print(f"PG engine_string: {self.db_url}")
        self.engine = self.get_pg_engine()
        self.validate_connection()
        # self.mysql_client = self.get_pg_client()
        # self.mysql_session = self.get_db_session()

    def get_pg_engine(self):
        try:
            db_url = self.db_url
            return create_engine(db_url)
        except Exception as e:
            print(f"Failed to get POSTGRES engine object: {e}")
            raise e

    def validate_connection(self):
        try:
            self.engine.connect()
            print("Successfully connected to Postgres Database!")
            return True
        except Exception as e:
            print(e)
            return False

    # The ORM’s “handle” to the database is the Session
    def get_db_session(self):
        db_session = sessionmaker(bind=self.engine)
        session_ins = db_session()
        return session_ins

    # Execute Pure SQL Statement
    # We should prevent the SQL injection, so not recommend for this function
    def execute_sql_statement(self, sql_statement):
        try:
            with self.engine.connect() as con:
                res = con.execute(sql_statement)
                return res
        except Exception as e:
            print(e)
            return None


PG_DB_INS = PostgresClient.get_instance(PG_DB_NAME)
PG_DB_SERVICE_INS = PostgresClient.get_instance(PG_SERVICE_DB_NAME)
PG_DB_PRODUCT = PostgresClient.get_instance(PG_DB_NAME)
PG_DB_AUTH_INS = PostgresClient.get_instance(PG_AUTH_DB_NAME)


def _get_metadata(records):
    log_settings = {}
    for record in records:
        log_settings[record.key] = record.value
    return log_settings


def get_metadata_auth():
    with PG_DB_SERVICE_INS.get_db_session() as pg_session:
        records = pg_session.query(postgres_orm.MetadataAuthTable)
        return _get_metadata(records)

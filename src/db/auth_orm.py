from sqlalchemy import <PERSON>um<PERSON>, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, JSON, Foreign<PERSON>ey, ARRAY
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

Base = declarative_base()


class SiteRecsTable(Base):
    """
    SQLAlchemy model for the auth.site_recs table that stores site recommendation configurations.
    """
    __tablename__ = 'site_recs'
    __table_args__ = {'schema': 'auth'}

    # Primary key that references site_info.id
    site_info_id = Column(Integer, ForeignKey('auth.site_info.id'), primary_key=True)

    # API and server configuration fields
    recommender_api_key = Column(String(255))
    data_server_url = Column(String(1024))
    data_server_project_api_key = Column(String(255))
    data_server_project_id = Column(String(255))
    data_server_query_api_key = Column(String(255))


class SiteProviderInfoTable(Base):
    __tablename__ = "site_provider_info"
    __table_args__ = {"schema": "auth"}  # Add the schema name to the table arguments

    id = Column(Integer, nullable=False, primary_key=True)
    issuer = Column(String, nullable=False)
    owner = Column(String, nullable=False)
    platform_type = Column(Integer, nullable=False)

    module_name = Column(String, nullable=True)
    class_name = Column(String, nullable=True)

    partner_key = Column(String, nullable=True)
    partner_secret = Column(String, nullable=True)
    api_version = Column(String, nullable=True)
    fields = Column(JSON, nullable=True)


class SiteInfoTable(Base):
    __tablename__ = "site_info"
    __table_args__ = {"schema": "auth"}  # Add the schema name to the table arguments

    id = Column(Integer, nullable=False, primary_key=True)

    site_type = Column(Integer, nullable=False, default=1)
    site_id = Column(String)
    name = Column(String)
    owner = Column(String)
    email = Column(String)
    domain = Column(String)
    country_name = Column(String)
    country_code = Column(String)
    currency = Column(String)
    iana_timezone = Column(String)

    # recommender_api_key = Column(String)

    # data_server_url = Column(String)
    # data_server_project_api_key = Column(String)
    # data_server_project_id = Column(String)

    # data_server_query_api_key = Column(String)

    created_at = Column(String)
    updated_at = Column(String)

    site_metadata = Column(JSON)


class SiteConnectionTable(Base):
    __tablename__ = "site_connection"
    __table_args__ = {"schema": "auth"}  # Add the schema name to the table arguments

    id = Column(Integer, nullable=False, primary_key=True)
    site_type = Column(Integer, nullable=False, default=1)
    status = Column(Integer, default=0)

    org_id = Column(Integer, nullable=False)
    user_id = Column(Integer, nullable=False)
    sale_user_id = Column(Integer, nullable=False)
    site_url = Column(String)
    scopes = Column(ARRAY(Integer))
    # approved_services = Column(ARRAY(Integer))

    site_info_id = Column(Integer, ForeignKey("auth.site_info.id"))
    auth_url = Column(String)
    redirected_url = Column(String)
    access_token = Column(String)
    auth_code = Column(String)

    shop_alias = Column(String)

    connect_dt = Column(DateTime, nullable=False)
    expire_time = Column(DateTime, nullable=False)
    role_type = Column(Integer)

    # Establishing the relationship
    site_info = relationship("SiteInfoTable")


class RecsInitLogTable(Base):
    __tablename__ = "recs_init_log"
    __table_args__ = {"schema": "recommendation"}

    id = Column(Integer, nullable=False, primary_key=True)
    site_id = Column(Integer, nullable=False)
    rec_type = Column(Integer, nullable=False)

    init_start_time = Column(DateTime)
    init_end_time = Column(DateTime)

    collection_ids = Column(ARRAY(String))


class MetadataERPSettingTable(Base):
    __tablename__ = "erp_setting"
    __table_args__ = {"schema": "metadata"}

    key = Column(String, nullable=False, primary_key=True)
    value = Column(String, nullable=True)
    comment = Column(String, nullable=True)

class MetadataLogSettingTable(Base):
    __tablename__ = "log_setting"
    __table_args__ = {"schema": "metadata"}

    key = Column(String, nullable=False, primary_key=True)
    value = Column(String, nullable=True)
    comment = Column(String, nullable=True)


class MetadataServiceTable(Base):
    __tablename__ = "service_setting"
    __table_args__ = {"schema": "metadata"}

    key = Column(String, nullable=False, primary_key=True)
    value = Column(String, nullable=True)
    comment = Column(String, nullable=True)

from sqlalchemy import select
from sqlalchemy import update
from sqlalchemy.ext.asyncio import AsyncSession

from db.pg_async_base_crud import AsyncBaseCRUD
from db.pg_orm import VariantsSyncTable
from utility.log import get_logger

log = get_logger(__name__)


class VariantsSyncCRUD(AsyncBaseCRUD[VariantsSyncTable]):
    def __init__(self):
        super().__init__(VariantsSyncTable)

    @staticmethod
    async def get_variant_by_products_sync_id_and_target_variant_id(
            pg_session: AsyncSession, products_sync_id: int, target_variant_id: str
    ) -> VariantsSyncTable | None:
        stmt = (
            select(VariantsSyncTable)
            .where(VariantsSyncTable.products_sync_id == products_sync_id)
            .where(VariantsSyncTable.target_variant_id == target_variant_id)
        )
        result = await pg_session.execute(stmt)
        return result.scalars().first()

    @staticmethod
    async def update_variant_price_and_stock(
            pg_session: AsyncSession,
            source_site_connection_ids: list[int],
            source_product_id: str,
            source_variant_id: str,
            stock_quantity: int | None = None,
            price: str | None = None
    ):
        try:
            if stock_quantity is None and price is None:
                return

            valid_data = {}
            if stock_quantity is not None:
                valid_data["stock_quantity"] = stock_quantity
            if price not in [None, ""]:
                valid_data["price"] = price

            stmt = (
                update(VariantsSyncTable)
                .where(VariantsSyncTable.source_site_connection_id.in_(source_site_connection_ids))
                .where(VariantsSyncTable.source_product_id == source_product_id)
                .where(VariantsSyncTable.source_variant_id == source_variant_id)
                .values(valid_data)
                .returning(VariantsSyncTable)
            )
            await pg_session.execute(stmt)
            await pg_session.flush()
        except Exception as e:
            log.exception(e)
            raise e

    @staticmethod
    async def get_target_site_connection_variants_stock_quantity(
            pg_session: AsyncSession, source_site_connection_ids: list[int], source_product_ids: list[str],
            source_variant_ids: list[str]
    ):
        try:
            stmt = (
                select(VariantsSyncTable)
                .where(VariantsSyncTable.source_site_connection_id.in_(source_site_connection_ids))
                .where(VariantsSyncTable.source_product_id.in_(source_product_ids))
                .where(VariantsSyncTable.source_variant_id.in_(source_variant_ids))
            )
            result = await pg_session.execute(stmt)
            return result.scalars().all()
        except Exception as e:
            log.exception(e)
            raise e


variants_sync_crud = VariantsSyncCRUD()

import logging
import os
import smtplib
from datetime import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from logging.handlers import <PERSON><PERSON><PERSON><PERSON><PERSON>, RotatingFileHandler

from db.metadata import get_metadata_log_setting

is_local_debug_str = os.getenv('LOCAL_DEBUG')
is_local_debug = (is_local_debug_str is not None) and (is_local_debug_str.lower() == 'true')

log_setting_metadata = get_metadata_log_setting()

audit_log_file_path = '/tmp/logs' if is_local_debug else log_setting_metadata.get('AUDIT_LOG_FILE', '/tmp/logs')

if not os.path.exists(audit_log_file_path):
    os.makedirs(audit_log_file_path)

# CRITICAL = 50
# FATAL = CRITICAL
# ERROR = 40
# WARNING = 30
# WARN = WARNING
# INFO = 20
# DEBUG = 10
# NOTSET = 0
log_level = int(log_setting_metadata.get('LOG_LEVEL', 20))

# Set up email notification
# print(f' log_setting_metadata: {log_setting_metadata}')
log_email_send_level = int(log_setting_metadata.get('LOG_EMAIL_SEND_LEVEL', 50))
email_from = log_setting_metadata.get('LOG_EMAIL_FROM', '<EMAIL>')
email_to = log_setting_metadata.get('LOG_EMAIL_TO', '<EMAIL>').split(',')
email_subject = log_setting_metadata.get('LOG_EMAIL_SUBJECT')
email_smtp_server = log_setting_metadata.get('LOG_EMAIL_SERVER', 'smtp.gmail.com')
email_smtp_port = int(log_setting_metadata.get('LOG_EMAIL_PORT', 587))
email_username = log_setting_metadata.get('LOG_EMAIL_USERNAME')
email_password = log_setting_metadata.get('LOG_EMAIL_PASSWORD')


class CustomSMTPHandler(SMTPHandler):
    def emit(self, record):
        try:
            smtp_server = smtplib.SMTP(self.mailhost, self.mailport)
            smtp_server.starttls()
            smtp_server.login(self.username, self.password)
            msg = MIMEMultipart()
            msg['Subject'] = self.getSubject(record)
            msg['From'] = self.fromaddr
            msg['To'] = ", ".join(self.toaddrs)
            body = self.format(record)
            body = MIMEText(body)
            msg.attach(body)
            smtp_server.sendmail(self.fromaddr, self.toaddrs, msg.as_string())
            smtp_server.quit()
        except (KeyboardInterrupt, SystemExit):
            print(f'!!! KeyboardInterrupt for CustomSMTPHandler!')
        except Exception as e:
            logging.exception(e)
            self.handleError(record)


# Create a handler that sends an email notification for errors
mail_handler = CustomSMTPHandler(mailhost=(email_smtp_server, email_smtp_port),
                                 fromaddr=email_from,
                                 toaddrs=email_to,
                                 subject=email_subject,
                                 credentials=(email_username, email_password),
                                 secure=None)

# Set the email handler level to ERROR
mail_handler.setLevel(log_email_send_level)


def send_email_msg(subject: str, name: str, msg: str):
    customised_mail_handler = CustomSMTPHandler(
        mailhost=(email_smtp_server, email_smtp_port),
        fromaddr=email_from,
        toaddrs=email_to,
        subject=subject,
        credentials=(email_username, email_password),
        secure=None
    )
    customised_mail_handler.setLevel(20)
    customised_mail_handler.emit(
        logging.LogRecord(
            name=name,
            level=0,
            pathname='',
            lineno=0,
            msg=msg,
            args=None,
            exc_info=None
        )
    )


def get_logger(app_name):
    """get_logger

    Arguments:
        app_name {str} -- webapp
        level {str} -- 'info' or 'debug'

    Returns:
        logger -- logger
    """
    # Create a custom logger
    logger = logging.Logger(app_name, level=log_level)

    log_formatter = logging.Formatter("%(asctime)s - %(name)s - %(funcName)s - %(levelname)s - %(message)s")

    handler_stream = logging.StreamHandler()
    handler_stream.setFormatter(log_formatter)

    now = datetime.now()
    file_name = now.strftime(f"{audit_log_file_path}/logfile-%Y-%m-%d.log")
    file_handler = RotatingFileHandler(file_name, maxBytes=5 * 1024 * 1024, backupCount=9)
    file_handler.setFormatter(log_formatter)

    logger.addHandler(file_handler)
    logger.addHandler(handler_stream)
    logger.addHandler(mail_handler)  # Add the email handler to the logger

    return logger

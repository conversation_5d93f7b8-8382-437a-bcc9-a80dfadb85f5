import asyncio
import os
from datetime import timed<PERSON><PERSON>
from io import Bytes<PERSON>
from timeit import default_timer as timer

import aiofiles
import aiohttp
import numpy as np
from aiohttp import ClientSession, ClientResponseError
from pandas import DataFrame
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from utility.exception_handler import RemoteServiceTimeout
from utility.log import get_logger

LOGGER = get_logger(__name__)

image_headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                  "Chrome/91.0.4472.124 Safari/537.36"}


product_image_download_folder = os.getenv("DATA_LAKE_ROOT")


@retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, max=10),
        retry=(retry_if_exception_type(RemoteServiceTimeout))  # 仅重试 RemoteServiceTimeout
    )
async def get_image_stream(image_url):
    _logger = get_logger(__name__)
    try:
        async with ClientSession() as client:
            async with client.get(image_url, headers=image_headers) as response:
                response.raise_for_status()  # Raises an exception for bad status codes
                content = await response.read()

                if content:
                    file_bytes = await asyncio.to_thread(
                        lambda: np.asarray(bytearray(BytesIO(content).read()), dtype=np.uint8)
                    )
                else:
                    file_bytes = None

                return image_url, file_bytes
    except ClientResponseError as e:
        _logger.warning(f"{e}")
        return image_url, None
    except TimeoutError as e:
        _logger.warning(f"{e}")
        return image_url, None
    except Exception as e:
        _logger.warning(f"{e}")
        return image_url, None


async def async_download_images_stream(image_urls):
    coroutines = [
        get_image_stream(image_url=image_url)
        for image_url in image_urls
    ]

    return list(await asyncio.gather(*coroutines))


async def async_download_image(image_url_tuple,
                               download_dir: str) -> None:
    LOGGER.info(f"before: {image_url_tuple}")
    sku, image_url, image_name = image_url_tuple
    image_filepath = os.path.join(download_dir, image_name)
    LOGGER.info(f">>> async_download_image: {image_filepath}...")
    async with aiohttp.ClientSession() as session:
        async with session.get(image_url) as response:
            if response.status == 200:
                content = await response.read()
                async with aiofiles.open(image_filepath, "wb") as f:
                    await f.write(content)
            else:
                LOGGER.error(f"Unable to download image {sku} from {image_url}")


async def async_download_images(image_url_tuples,
                                download_dir: str) -> None:
    if not os.path.exists(download_dir):
        os.mkdir(download_dir)

    coroutines = [
        async_download_image(image_url_tuple=image_url_tuple,
                             download_dir=download_dir)
        for image_url_tuple in image_url_tuples if image_url_tuple[1]
    ]

    await asyncio.gather(*coroutines)


async def download_images(df: DataFrame, download_dir: str):
    LOGGER.info("Downloading images...")
    start = timer()

    image_url_tuples = list(df[["sku", "image_src", "image_name"]].to_records(index=False))

    await async_download_images(image_url_tuples=image_url_tuples, download_dir=download_dir)

    end = timer()
    LOGGER.info(f"Download Time Elapsed: {timedelta(seconds=end - start)}")


def get_image_path(website_code: str):
    path = f"{product_image_download_folder}/website_data/{website_code}/images"
    if not os.path.exists(path):
        os.makedirs(path)
    return path


async def async_download_single_image(image_url_tuple, website_code: str) -> None:
    LOGGER.info(f"before: {image_url_tuple}")
    shop_sku_id, image_url, image_name = image_url_tuple

    image_path = get_image_path(website_code)

    image_filepath = os.path.join(image_path, image_name)
    LOGGER.info(f">>> download image and store in: {image_filepath}...")
    async with aiohttp.ClientSession() as session:
        async with session.get(image_url) as response:
            if response.status == 200:
                content = await response.read()
                async with aiofiles.open(image_filepath, "wb") as f:
                    await f.write(content)
            else:
                LOGGER.error(f"Unable to download image {shop_sku_id} from {image_url}")


async def async_download_full_images(image_url_tuples, website_code: str) -> None:
    """
    This function is modified version of
    async def async_download_images(image_url_tuples, website_code: str)
    """
    coroutines = [
        async_download_single_image(image_url_tuple=image_url_tuple, website_code=website_code)
        for image_url_tuple in image_url_tuples if image_url_tuple[1]
    ]

    await asyncio.gather(*coroutines)


async def download_full_images(df: DataFrame, website_code: str):
    """
    This function is modified version of
    async def download_images(df: DataFrame, website_code: str)
    """
    LOGGER.info("Downloading images...")
    start = timer()
    image_url_tuples = list(df[["shop_sku_id", "image_src", "image_name"]].to_records(index=False))

    await async_download_full_images(image_url_tuples=image_url_tuples, website_code=website_code)

    end = timer()
    LOGGER.info(f"Download Time Elapsed: {timedelta(seconds=end - start)}")

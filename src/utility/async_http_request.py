import aiohttp
from aiohttp import ClientSession, ClientResponseError
from fastapi import HTTPException
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from utility.exception_handler import parse_http_response, RemoteServiceTimeout, RemoteServiceError
from utility.log import get_logger

log = get_logger(__name__)

default_headers = {
    "Content-Type": "application/json;charset=UTF-8"
}


@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, max=10),
    retry=(retry_if_exception_type(RemoteServiceTimeout))  # 仅重试 RemoteServiceTimeout
)
async def aiohttp_call(rpc_url, payload=None, method="GET", timeout: int = 240, headers=None):
    if headers is None:
        headers = default_headers
    if payload is None:
        payload = {}

    query_paras = {
        "method": method,
        "url": rpc_url,
        "headers": headers,
        "timeout": timeout
    }

    # Convert boolean values to strings
    if isinstance(payload, dict):
        payload = {k: str(v).lower() if isinstance(v, bool) else v for k, v in payload.items()}

    if method == "GET":
        query_paras["params"] = payload
    else:
        query_paras["data"] = payload

    # log.info(f"> query_paras: {query_paras}")

    async with ClientSession(connector=aiohttp.TCPConnector(ssl=False)) as session:
        try:
            async with session.request(**query_paras) as response:
                json_response, response_status = await parse_http_response(response, query_paras)
                return json_response

        except aiohttp.ContentTypeError:
            log.error("JSON decode error")
            raise HTTPException(status_code=500, detail="JSON decode error")
        except aiohttp.client_exceptions.ClientConnectionError as e:
            log.warning(f"ClientConnectionError: {str(e)}")
            return str(e)
        except TimeoutError as e:
            log.warning(f"TimeoutError: {str(e)}")
            return str(e)
        except ClientResponseError as e:
            log.error(f"ClientResponseError: {str(e)}")
            raise HTTPException(status_code=e.status, detail=str(e))


class AsyncHttpRequest:
    def __init__(self, failed_retry_num=3):
        """
        AsyncHttpRequest init
        :param failed_retry_num: if there are failed response in self.failed_key_set, re-run the same logic.
        """
        self.log = get_logger(self.__class__.__name__)
        self.failed_retry_num = failed_retry_num
        self.failed_num = 0
        self.failed_key_dict = dict()

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, max=10),
        retry=(retry_if_exception_type(RemoteServiceTimeout))  # 仅重试 RemoteServiceTimeout
    )
    async def get_http_request_async(
            self, sess, url, key=None, payload=None, method="POST", header=None, timeout=3000
    ):
        if payload is None:
            payload = {}
        if header is None:
            header = default_headers

        para_dict = {
                        "method": method,
                        "url": url,
                        "headers": header,
                        "timeout": timeout
                    } | {"data" if method == "POST" else "params": payload}

        response = await sess.request(**para_dict)

        if response:
            return await parse_http_response(response, para_dict)

        if key:
            self.failed_key_dict[key] = payload
        return None, 500

    async def http_request(self, url, payload, method="POST", timeout: int = 3000, headers=None):
        try:
            async with ClientSession() as session:
                response, response_status = await self.get_http_request_async(
                    session, url=url, payload=payload, method=method, header=headers, timeout=timeout
                )
                return response, response_status
        except aiohttp.ContentTypeError:
            log.error("JSON decode error")
            raise HTTPException(status_code=500, detail="JSON decode error")
        except aiohttp.client_exceptions.ClientConnectorError as e:
            raise RemoteServiceError(exception_detail=f"Remote server connection error with {url}: {str(e)}")
        except TimeoutError as e:
            log.warning(f"TimeoutError: {str(e)}")
            return str(e)
        except ClientResponseError as e:
            log.error(f"ClientResponseError: {str(e)}")
            raise HTTPException(status_code=e.status, detail=str(e))

    async def run_program(self, sess, url, key, payload, response_parse_method=None, method="POST", header=None):
        response, response_status = await self.get_http_request_async(
            sess, url, key, payload, method=method, header=header
        )

        if response:
            if response_status != 200:
                return None

            if response_parse_method:
                return response_parse_method(key, response)
            # log.info(f"Response: {json.dumps(parsed_response, indent=2)}")
            return response

        return None

    async def _http_request(self, url, payloads: dict, response_parse_method, method="POST", header=None):
        """
        Run the async calling method.

        :param url: http request target like:
        f'{website_url}/api/admin/product/sku/update.json?access_token={access_token} '
        :param payloads: dict type. {key: payload}. key is the sku_id or others to locate and redo the failed request.
        :param response_parse_method: the method how to parse the returned
        :param method: POST, GET

        the system would reload the failed requests again with <failed_retry_num> times.
        :return:
        """
        try:
            async with ClientSession() as session:
                return {
                    key: await self.run_program(
                        session, url, key, payload, response_parse_method, method=method, header=header
                    ) for key, payload in
                    payloads.items()
                }
        except aiohttp.client_exceptions.ClientConnectorError as e:
            raise RemoteServiceError(exception_detail=f"Remote server connection error with {url}: {str(e)}")

    # @timeit
    async def run(
            self, url,
            payloads: dict,
            response_parse_method=None,
            method="POST", header=None,
            parallel_count=200,
            is_dict_result=False
    ):
        """

        Args:
            is_dict_result:
            url:
            payloads:
            response_parse_method:
            method:
            header:
            parallel_count: Ali max 200 qps

        Returns:

        """
        results = dict()

        payloads_list = list(payloads.items())

        for payloads_part in [dict(payloads_list[i: i + parallel_count]) for i in range(
                0, len(payloads_list), parallel_count)]:
            results_part = await self._http_request(
                url, payloads_part, response_parse_method,
                method=method, header=header
            )

            if results_part:
                results |= results_part

        while len(self.failed_key_dict) > 0 and (self.failed_num < self.failed_retry_num):
            self.failed_num += 1
            # self.log.info(
            #     f"Re-run {self.failed_num} the request for failed requests with len {len(self.failed_key_dict)}!")
            rerun_payloads = self.failed_key_dict.copy()
            self.failed_key_dict.clear()
            new_results = await self._http_request(
                url, rerun_payloads, response_parse_method, method=method
            )

            if new_results:
                results |= new_results

        # self.log.info(f"Async {method} request finished.")

        if is_dict_result:
            all_results = results
        else:
            all_results = list(results.values())

        # self.log.info(f' all_results: {all_results}')

        return all_results

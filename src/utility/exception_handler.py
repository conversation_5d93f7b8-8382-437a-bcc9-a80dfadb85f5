from functools import wraps

import aiohttp
from fastapi import HTTPException
from starlette.status import *

from utility.log import get_logger

log = get_logger(__name__)


class BusinessException(HTTPException):
    def __init__(
            self, message: str, exception_detail: str | None = None,
            status_code: int = HTTP_500_INTERNAL_SERVER_ERROR
    ):
        detail = {"message": message}
        if exception_detail:
            detail["exception"] = exception_detail
        super().__init__(status_code=status_code, detail=detail)


class UserError(BusinessException):
    def __init__(
            self,
            message: str,
            exception_detail: str | None = None,
            status_code=HTTP_422_UNPROCESSABLE_ENTITY):
        log.warning(f"{message}: {exception_detail}")
        super().__init__(message=message, exception_detail=exception_detail, status_code=status_code)


class ExternalServiceError(BusinessException):
    def __init__(
            self,
            message: str,
            exception_detail: str | None = None,
            status_code=HTTP_503_SERVICE_UNAVAILABLE):
        log.warning(f"{message}: {exception_detail}")
        super().__init__(message=message, exception_detail=exception_detail, status_code=status_code)


class InternalServiceError(BusinessException):
    def __init__(
            self,
            message: str,
            exception_detail: str | None = None,
            status_code=HTTP_500_INTERNAL_SERVER_ERROR):
        log.exception(f"{message}: {exception_detail}")
        super().__init__(message=message, exception_detail=exception_detail, status_code=status_code)


class AuthorizationError(UserError):
    def __init__(self, message: str = "UNAUTHORIZED REQUEST", exception_detail: str | None = None):
        super().__init__(message=message, exception_detail=exception_detail, status_code=HTTP_401_UNAUTHORIZED)


class AppValueError(UserError):
    def __init__(self, message: str = "NOT ACCEPTABLE VALUE", exception_detail: str | None = None):
        super().__init__(message=message, exception_detail=exception_detail, status_code=HTTP_406_NOT_ACCEPTABLE)


class InputValidationError(UserError):
    def __init__(self, message: str = "UNPROCESSABLE INPUT ENTITY", exception_detail: str | None = None):
        super().__init__(message=message, exception_detail=exception_detail, status_code=HTTP_422_UNPROCESSABLE_ENTITY)


class RemoteServiceTimeout(ExternalServiceError):
    def __init__(self, message: str = "REMOTE SERVICE TIMEOUT", exception_detail: str | None = None):
        super().__init__(message=message, exception_detail=exception_detail, status_code=HTTP_504_GATEWAY_TIMEOUT)

class RemoteServiceError(ExternalServiceError):
    def __init__(self, message: str = "REMOTE SERVICE ERROR", exception_detail: str | None = None):
        super().__init__(message=message, exception_detail=exception_detail, status_code=HTTP_424_FAILED_DEPENDENCY)

def exception_handler_decorator(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except (BusinessException, HTTPException):
            # 已知异常，不处理，交给全局异常处理器
            raise
        except AssertionError as e:
            # 断言失败转为 InternalServiceError
            raise InternalServiceError(message='ASSERTION ERROR', exception_detail=str(e), status_code=500)
        except Exception as e:
            # 未知异常统一为 InternalServiceError
            raise InternalServiceError(message='INTERNAL SERVICE ERROR', exception_detail=str(e), status_code=500)

    return wrapper

def _get_exception_str(msg: str, payload: dict | None = None):
    return msg + (f" with http payload: {payload}" if payload else "")


async def parse_http_response(response, payload: dict | None = None):
    if response.status == 404:
        raise AppValueError(exception_detail=_get_exception_str("Cannot find auth URL", payload))
    elif response.status == 401:
        raise AuthorizationError(
            exception_detail=_get_exception_str("Check API Key or Authentication Token", payload)
        )
    elif response.status in [400, 403]:
        res = await response.text()
        raise AuthorizationError(
            exception_detail=_get_exception_str(res, payload)
        )
    elif response.status == 422:
        res = await response.text()
        raise InputValidationError(
            exception_detail=_get_exception_str(res, payload)
        )
    elif response.status == 504:
        res = await response.text()
        raise RemoteServiceTimeout(exception_detail=_get_exception_str(f"Gateway Time-out: {res}", payload))
    elif response.status == 500:
        res = await response.text()
        if 'HSFTimeOutException' in res:
            raise RemoteServiceTimeout(exception_detail=_get_exception_str(res, payload))

        raise RemoteServiceError(exception_detail=_get_exception_str(f"Remote server error: {res}", payload))
    elif response.status == 502:
        res = await response.text()
        raise RemoteServiceError(exception_detail=_get_exception_str(f"Bad Gateway: {res}", payload))
    else:
        response.raise_for_status()

    # Check the content type
    content_type = response.headers.get("Content-Type", "").lower()

    if "application/json" in content_type:
        try:
            json_response = await response.json()
            return json_response, 200
        except aiohttp.ContentTypeError:
            raise RemoteServiceError(exception_detail=_get_exception_str(f"JSON decode error", payload))
    elif "text/plain" in content_type:
        return await response.text(), 200
    else:
        raise RemoteServiceError(
            exception_detail=_get_exception_str(f"Unexpected content type: {content_type}", payload)
        )

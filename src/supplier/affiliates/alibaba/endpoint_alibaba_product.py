from __future__ import annotations

import json
import math

from fastapi import APIRouter, Depends, Header
from fastapi_versioning import version

from supplier.affiliates.alibaba.ali_data_service import ali_api_call, get_1688_product_detail_payload, \
    query_product_details_parallel, \
    handle_ali_api_product_result, handle_ali_vendor_product_ids, get_ali_product_details, \
    api_namespace_product, query_top_list_products
from utility.api_helper import success_response_handler
from utility.log import get_logger
from utility.oauth import validate_user_plan

router_alibaba_product = APIRouter(
    prefix="/supplier/alibaba/product",
    tags=["Alibaba Product APIs"],
)


log = get_logger(__name__)

@router_alibaba_product.get("/top-list")
async def get_product_by_keyword(
        category_id: int | None = None,
        rank_type: str = "complex",
        limit: int = 10,
        language: str = "en",
        site_connection_id: int = Header(...)
):
    products = await query_top_list_products(
        site_connection_id=site_connection_id,
        category_id=category_id,
        rank_type=rank_type,
        limit=limit,
        language=language
    )
    return success_response_handler(
        data=products,
        msg=f"Get queried products"
    )

@router_alibaba_product.get("/keyword")
@version(1, 0)
async def get_product_by_keyword(
        keyword: str | None = None,
        begin_page: int = 1,
        page_size: int = 30,
        product_filter: str | None = None,
        sort: str = '{"monthSold":"desc"}',
        price_start: str | None = None,
        price_end: str | None = None,
        category_id: int | None = None,
        language: str = "en",
        month_sold: int = None,
        vendor_product_ids: str | None = None,
        site_connection_id: int = Header(...)
):
    """
    Get product from all vendors by keyword

    Args:
        month_sold:
        vendor_product_ids: vendor products - use product detail API to fetch the simple product info
        keyword: Not null. like skirt
        begin_page: Not null. like 1
        page_size: Not null. like 20
        product_filter: 筛选参数，多个通过英文逗号分隔，枚举参见解决方案介绍. Like shipInToday,ksCiphertext
        sort: 排序参数，枚举参见解决方案介绍 Like {"price":"asc"}

        price_start: 批发价开始 Like 1
        price_end: 批发价结束 Like 10
        category_id: like 1
        language: Not null.
        site_connection_id:

    Returns:
        {
          "imageUrl": "https://cbu01.alicdn.com/img/ibank/2020/414/104/22289401414_1954021166.jpg",
          "isJxhy": true,  是否精选货源
          "isOnePsale": true, 是否一件代发
          "monthSold": 33, 30天销量
          "offerId": ************,  商品id
          "priceInfo": {
            "consignPrice": "27.00", 一件代发价
            "jxhyPrice": "32",  代发精选货源价
            "pfJxhyPrice": "22", 批发精选货源价
            "price": "22.00"   批发价
          },
          "repurchaseRate": "20%", 复购率
          "subject": "jk制服裙学院风纯色裙不良长裙日系绀色百褶裙黑色校服裙女高腰裙",
          "subjectTrans": "jk Uniform Skirt Academic Style Solid Color Skirt Bad Long Skirt Japanese Style Cyan
          Color Pleated Skirt Black School Uniform Skirt Women's High Waist Skirt",
          "traceInfo": "object_id@************^object_type@offer" 向1688上报打点数据
        }

    """

    # async def add_variant_count(products):
    #     result = []
    #     for product in products:
    #         product["variant_count"] = await get_variant_count(None, site_connection_id, product["offerId"])
    #         result.append(product)
    #     return result

    if vendor_product_ids:
        vendor_product_ids = [int(p) for p in vendor_product_ids.split(",")]

        total_records = len(vendor_product_ids)
        total_pages = math.ceil(total_records / page_size)

        if begin_page < 1:
            begin_page = 1

        if begin_page > total_pages:
            begin_page = total_pages

        start_index = (begin_page - 1) * page_size
        end_index = begin_page * page_size

        paged_product_ids = vendor_product_ids[start_index:end_index]

        payload_list_dict = {
            p: get_1688_product_detail_payload(
                offer_id=p,
                language=language
            ) for p in paged_product_ids
        }

        product_details = await query_product_details_parallel(
            payload_list_dict=payload_list_dict,
            site_connection_id=site_connection_id
        )

        page_info = {
            "totalRecords": total_records,
            "totalPage": total_pages,
            "pageSize": page_size,
            "currentPage": begin_page,
            "data": handle_ali_vendor_product_ids(product_details)
        }

        return success_response_handler(
            data=page_info,
            msg=f"Get queried products from vendor_product_ids {paged_product_ids}"
        )

    log.info(f"category_id: {category_id}, language: {language}, site_connection_id {site_connection_id}")
    # plan_checker(plan_info)

    api_name = "product.search.keywordQuery"
    offer_query_param = {
        "keyword": keyword,
        "beginPage": begin_page,
        "pageSize": page_size,
        "filter": product_filter,
        "sort": sort,
        "outMemberId": None,
        "priceStart": price_start,
        "priceEnd": price_end,
        "categoryId": category_id,
        "country": language
    }
    offer_query_param = {k: v for k, v in offer_query_param.items() if v}

    para_dict = {
        "offerQueryParam": json.dumps(offer_query_param).replace(" ", "")
    }
    # log.info(f"para_dict: {para_dict}")
    products = handle_ali_api_product_result(await ali_api_call(
        api_namespace=api_namespace_product,
        api_name=api_name,
        site_connection_id=site_connection_id,
        payload=para_dict,
        method="POST"
    ))
    if month_sold:
        products = [product for product in products if product["monthSold"] >= month_sold]
    log.info(f"products {products}")
    # products["data"] = await add_variant_count(products["data"])
    return success_response_handler(
        data=products,
        msg=f"Get queried products from keyword {keyword}"
    )


@router_alibaba_product.get("/details")
@version(1, 0)
async def get_product_details(
        offer_id: int,
        language: str = "en",
        site_connection_id: int = Header(...),
        _=Depends(validate_user_plan)
):
    product_details = await get_ali_product_details(offer_id, site_connection_id, language)

    return success_response_handler(
        data=product_details,
        msg=f"Get product detail from offer_id {offer_id}"
    )

#
# @router_alibaba_product.post("/sync_selected")
# @version(1, 0)
# async def sync_selected_products(
#         background_tasks: BackgroundTasks,
#         products_info: SyncedProductsInfo,
#         site_connection_id_source: int = Header(...),
#         site_connection_id_target: int = Header(...),
#         plan_info=Depends(validate_user_plan)
# ):
#     org_id = plan_info.get("org_id")
#
#     log.info(
#         f"org_id: {org_id}, "
#         f"site_connection_id_source {site_connection_id_source}, "
#         f"site_connection_id_target: {site_connection_id_target} "
#         f"month_sold_threshold: {products_info.month_sold_threshold} "
#         f"is_detect_main_images: {products_info.is_detect_main_images}"
#     )
#
#     async def products_etl():
#         website_detail = PGSiteConnection.get_website_detail(
#             site_connection_id=site_connection_id_target, org_id=org_id
#         )
#
#         isc_api_key = (website_detail.get("site_metadata") or {}).get("api_key")
#         assert isc_api_key, "isc_api_key cannot be None"
#
#         product_details_list = await get_product_details_with_item_ids(
#             site_connection_id=site_connection_id_source, item_ids=products_info.product_ids)
#
#         product_details_list_len = len(product_details_list)
#
#         if product_details_list_len > 0:
#             log.info(f">>> ETL products with {len(product_details_list)} products totally...")
#             await product_details_batch_etl(
#                 site_type=website_detail.get("site_type"),
#                 site_id=website_detail.get("site_id"),
#                 product_details=product_details_list[:products_info.synced_total_product_count]
#                 if products_info.synced_total_product_count > 0
#                 else product_details_list,
#                 isc_api_key=isc_api_key,
#                 is_publish=products_info.is_publish,
#                 is_detect_main_images=products_info.is_detect_main_images,
#                 is_detect_description_images=products_info.is_detect_description_images,
#                 max_sync_num=products_info.synced_product_count_per_page
#             )
#         else:
#             log.info(">>> There is no any products to sync.")
#
#     background_tasks.add_task(products_etl)
#
#     return success_response_handler(
#         data="importing...",
#         msg="Sync selected products from 1688 into EMUTREE"
#     )
#
#
# @router_alibaba_product.get("/update")
# @version(1, 0)
# async def isc_product_update(
#         background_tasks: BackgroundTasks,
#         keyword: str | None = None,
#         max_product_num: int | None = None,
#         category_id: int | None = None,
#         update_date_before: str | None = None,
#         create_time_start: str | None = None,
#         create_time_end: str | None = None,
#         write_time_start: str | None = None,
#         write_time_end: str | None = None,
#         start_product_loc: int = 0,
#         max_sync_num: int = 10,
#         is_detect_main_images: bool = False,
#         is_detect_description_images: bool = False,
#         site_connection_id_source: int = Header(...),
#         site_connection_id_target: int = Header(...),
#         plan_info=Depends(validate_user_plan)
# ):
#     """
#     Update product details from 1688 into EMUTREE existing product category
#
#     Args:
#         write_time_end: "2024-01-18T00:59:49"
#         write_time_start: "2024-01-18T00:59:49"
#         create_time_end: "2024-01-18T00:59:49"
#         create_time_start: "2024-01-18T00:59:49"
#         is_detect_main_images: "2024-01-18T00:59:49"
#         is_detect_description_images:
#         max_sync_num: numbers we can handle maximum in ETL
#         start_product_loc: for renewing the updating from breakpoint
#         background_tasks:
#         keyword:
#         max_product_num:
#         category_id: ISC product category id
#         update_date_before: "2024-01-18T00:59:49"
#         site_connection_id_source:
#         site_connection_id_target:
#         plan_info:
#
#     Returns:
#
#     """
#     org_id = plan_info.get("org_id")
#
#     log.info(
#         f"org_id: {org_id}, "
#         f"category_id: {category_id}, "
#         f"site_connection_id_source {site_connection_id_source}, "
#         f"site_connection_id_target: {site_connection_id_target}"
#     )
#
#     # plan_checker(plan_info)
#
#     isc_connection = get_site_connection_info(site_connection_id=site_connection_id_target)
#     website_detail = get_website_info(isc_connection.id)
#     isc_api_key = website_detail.site_metadata.get("api_key")
#     assert isc_api_key, "No API key provided"
#
#     vendor_connection = get_site_connection_info(site_connection_id=site_connection_id_source)
#     vendor_site_detail = get_website_info(vendor_connection.id)
#
#     log.info(f"vendor_site_detail: {vendor_site_detail.__dict__}, site_type: {vendor_site_detail.site_type}")
#
#     async def products_update(language="en"):
#
#         # Fetch existing products from EMUTREE
#         total_products = await get_isc_product_total(
#             isc_api_key=isc_api_key,
#             keyword=keyword,
#             category_id=category_id,
#             create_time_start=create_time_start,
#             create_time_end=create_time_end,
#             write_time_start=write_time_start,
#             write_time_end=write_time_end
#         )
#         log.info(f"Got total {total_products} products.")
#         query_product_max_num = max_product_num if (
#                 max_product_num and (max_product_num < total_products)
#         ) else total_products
#         log.info(f"Fetch the first {query_product_max_num} products.")
#
#         page_size = 20
#         max_pages = math.ceil(query_product_max_num / page_size)
#
#         setup_max_pages = 2
#
#         ###################################################################################################
#         # Update products with page_size * setup_max_pages = 100 in each batch
#         for begin_page in range(1, max_pages + 1, setup_max_pages):
#             end_page = min(max_pages, begin_page + setup_max_pages - 1)
#
#             log.info(f"get_isc_products_vendor_info_parallel from {begin_page} to {end_page}.")
#             product_list_page_list = await get_isc_products_vendor_info_parallel(
#                 isc_api_key=isc_api_key,
#                 page_size=page_size,
#                 begin_page=begin_page,
#                 end_page=end_page,
#                 keyword=keyword,
#                 category_id=category_id,
#                 create_time_start=create_time_start,
#                 create_time_end=create_time_end,
#                 write_time_start=write_time_start,
#                 write_time_end=write_time_end
#             )
#
#             if not product_list_page_list:
#                 log.info("Cannot find any isc_products_vendor_info in parallel")
#                 continue
#
#             full_payload_list_dict = {}
#
#             for product_list in product_list_page_list:
#                 if update_date_before:
#                     payload_list_dict = {
#                         p.get("wd_product_id"): get_1688_product_detail_payload(
#                             offer_id=p.get("vendor_product_id"),
#                             language=language
#                         ) for p in product_list if (p.get("write_date") < update_date_before)
#                     }
#                     log.info(f"payload_list_dict less than {update_date_before} num: {len(payload_list_dict)}")
#                     full_payload_list_dict |= payload_list_dict
#                 else:
#                     payload_list_dict = {
#                         p.get("wd_product_id"): get_1688_product_detail_payload(
#                             offer_id=p.get("vendor_product_id"),
#                             language=language
#                         ) for p in product_list
#                     }
#                     log.info(f"payload_list_dict num: {len(payload_list_dict)}")
#                     full_payload_list_dict |= payload_list_dict
#
#             # Export product details
#             if full_payload_list_dict:
#                 log.info(f"Updating 1688 data info: {len(full_payload_list_dict.keys())}")
#             else:
#                 log.info("Cannot find any 1688_product_detail")
#                 continue
#
#             # The dict would be <wd_product_id>: <new product info from 1688>
#             product_details_dict = await query_product_details_parallel(
#                 payload_list_dict=full_payload_list_dict,
#                 site_connection_id=site_connection_id_source,
#                 is_dict_result=True
#             )
#
#             unlisted_products = [k for k, v in product_details_dict.items() if v is None]
#             log.info(f"Unlisted wd_product_id IDs in ISC: {unlisted_products}")
#
#             if unlisted_products:
#                 await archive_product(wd_product_ids=unlisted_products, isc_api_key=isc_api_key)
#
#             refreshed_products = [v for k, v in product_details_dict.items() if v]
#             log.info(f"Refreshed products len: {len(refreshed_products)}")
#
#             await product_details_batch_etl(
#                 site_type=vendor_site_detail.site_type,
#                 site_id=vendor_site_detail.site_id,
#                 product_details=refreshed_products,
#                 isc_api_key=isc_api_key,
#                 start_product_loc=start_product_loc,
#                 max_sync_num=max_sync_num,
#                 is_detect_main_images=is_detect_main_images,
#                 is_detect_description_images=is_detect_description_images
#             )
#
#     background_tasks.add_task(products_update)
#
#     return success_response_handler(
#         data="Updating...",
#         msg="Updating product details from 1688 into EMUTREE!"
#     )

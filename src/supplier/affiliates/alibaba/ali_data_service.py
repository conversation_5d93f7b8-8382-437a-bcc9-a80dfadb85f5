from __future__ import annotations

import hmac
import json
import os
from typing import Optional

from db.auth_orm import SiteInfoTable
from db.pg_site_connection import get_site_connection_info
from supplier.affiliates.alibaba.utils.common_util import build_invoke_url_path
from utility.api_helper import AppValidationError, AppValueError
from utility.async_http_request import aiohttp_call, AsyncHttpRequest
from utility.log import get_logger

log = get_logger(__name__)

vendor_data_path = os.getenv("LOCAL_FS_PATH", "/datalake/vendor_data")

ali_query_languages = ["en"]
api_namespace_product = "com.alibaba.fenxiao.crossborder"


def create_vendor_data_path():
    if not os.path.exists(vendor_data_path):
        os.mkdir(vendor_data_path)


ali_header = {"Content-Type": "application/x-www-form-urlencoded"}

site_secret_map = {}

product_top_list_info_map = {
    "item_id": "offerId",
    "min_order_qty": "minOrderQuantity",
    "monthly_sold": "soldOut"
}

product_keyword_list_info_map = {
    "item_id": "offerId",
    "main_image_url": "imageUrl",
    "monthly_sold": "monthSold"
}

skipped_product_keywords = [
        "直播",
        "专拍",
        "勿拍",
        "赠品",
        "尾单",
        "尾款",
        "补邮",
        "链接"
]

top_list_category_ids = [
    10166,  # 女装
    312,  # 内衣

    54,  # 服饰配件、饰品
    97,  # 美容护肤/彩妆
    130822220,  # 个护/家清
    96,  # 家纺家饰

    122916001,  # 宠物及园艺
    1038378,  # 鞋
    1042954,  # 箱包皮具

    311,  # 童装
    1813,  # 玩具

    10165,  # 男装
    7,  # 数码、电脑
    18,  # 运动户外
    67,  # 办公、文化
]

product_list_keys = [
    # "imageUrl",
    "subject",
    "subjectTrans",
    "offerId",
    "isJxhy",
    # "priceInfo",
    # "repurchaseRate",
    # "monthSold",
    "traceInfo",
    # "isOnePsale",
    # "sellerIdentities",
    "offerIdentities",
    "tradeScore",
    # "whiteImage",
    "topCategoryId",
    "secondCategoryId",
    "thirdCategoryId",
    # "isPatentProduct",
    "createDate",
    # "modifyDate",
    "isSelect",
    "minOrderQuantity",
    "sellerDataInfo",
    # "productSimpleShippingInfo",
    "promotionURL"
]


def get_alibaba_api_signature(url_para: str, para_dict: dict, site_info: Optional[SiteInfoTable]):
    """
    调用url中必须带入签名信息，作为接入方将会得到appKey和appSecret，签名结果以_aop_signature=xxxx的参数形式发送到服务端。

    如：https://gw.open.1688.com/openapi/http/1/system/currentTime/1688?b=1&a=2&_aop_signature=2F1E96587451DE0E171978F4AAE1A90FF9B2F94B

    api签名，顾名思义签名结果跟api有关，主要跟urlPath（包含api基本信息）以及请求参数（api入参）有关，具体算法如下：

    1、 构造urlPath。urlPath是url 中的一部分，从协议开始截取，到“?”为止，包含了协议、namespace、apiName和apiVersion，
    如urlPath=http/1/system/currentTime/1688

    2、 拼装参数。首先将所有参数按照key+value拼装得到一个字符串数组（如[b1,a2]），
    然后对数组进行排序（[a2,b1]），最后将排序后的数组合并为字符串（a2b1）

    3、 合并。把前两步的字符串拼接（http/1/system/currentTime/1688a2b1）

    4、 执行hmac_sha1算法。 Signature=uppercase (hex (hmac_sha1 (concatString, secretKey))，假设secretKey=test123，
    那么得到的签名为2F1E96587451DE0E171978F4AAE1A90FF9B2F94B

        a) concatString为合并后的字符串

        b) secretKey为签名密钥，与urlPath中的appKey（1000000）对应

        c) hmac_sha1为通用的hmac_sha1算法，各编程语言一般都对应类库

        d) hex为转为十六进制

        e) uppercase为转为大写字符
    Args:
        site_info:
        url_para:
        para_dict:

    Returns:

    """
    # Sort the dictionary by keys
    sorted_dict = dict(sorted(para_dict.items()))

    para_url = "".join([f"{k}{v}" for k, v in sorted_dict.items() if v is not None]).replace("'", '"').replace(" ", "")

    url_encode = url_para + para_url
    # log.info(f" url_encode: {url_encode}")

    site_id = site_info.site_id
    if site_id in site_secret_map.keys():
        secret = site_secret_map[site_id]
    else:
        log.info(f"site_info: {site_info.site_metadata}")
        assert site_info.site_metadata, f"Cannot get correct site_info.site_metadata from {site_info.__dict__}"
        secret = site_info.site_metadata.get("app_secret") if site_info else None

        if not secret:
            raise AppValueError(
                message="Cannot get 1688 Site Connection app_secret!",
                exception_detail=f"Cannot get site {site_id} secret",
            )

        site_secret_map[site_id] = secret

    computed_hmac = (
        hmac.new(
            key=secret.encode("utf-8"),
            msg=url_encode.encode("utf-8"),
            digestmod="sha1",
        )
        .hexdigest()
        .upper()
    )

    return computed_hmac


def verify_hmac(hmac_signature_src: str, hmac_signature_tgt: str):
    return hmac.compare_digest(hmac_signature_src, hmac_signature_tgt)


def get_access_token(site_connection_id: int):
    """
    Get access_token from database. 1688 token has no expired time.
    Args:
        site_connection_id:

    Returns:

    """
    connection_info = get_site_connection_info(site_connection_id=site_connection_id)
    if not connection_info:
        # to remote call to get new access_token
        raise AppValidationError(
            message=f"Cannot get connection_info with site_connection_id: {site_connection_id}",
            exception_detail="Please connect the 1688 platform with your account correctly first!",
        )

    access_token = connection_info.access_token

    if not access_token:
        # to remote call to get new access_token
        raise AppValidationError(
            message="Cannot get access_token",
            exception_detail="Please connect the 1688 platform with your account correctly first!",
        )

    if not connection_info.site_info:
        raise AppValidationError(
            message="Cannot get site_info",
            exception_detail="Please check your connection id if the site has already been removed.",
        )

    return access_token, connection_info.site_info


def get_ali_api_call_para(api_namespace: str, api_name: str, site_connection_id: int, payload: dict, method="GET"):
    url_full, payloads = get_ali_api_para(api_namespace, api_name, site_connection_id, payload)

    if method == "GET":
        full_paras = "&".join([f"{k}={v}" for k, v in payload.items() if v])
        full_url = f"{url_full}?{full_paras}"
    else:
        full_url = url_full

    log.info(f"full_url: {full_url}")
    return full_url, payload


def get_ali_api_para(api_namespace: str, api_name: str, site_connection_id: int, payloads: dict):
    access_token, site_info = get_access_token(site_connection_id=site_connection_id)
    # log.info(f"Got access_token: {access_token}")

    url_full, url_para = build_invoke_url_path(
        api_namespace=api_namespace, api_name=api_name, api_key=site_info.site_id
    )

    # log.info(f"Web url_full: {url_full}, url_para: {url_para}")

    payloads["access_token"] = access_token
    sig = get_alibaba_api_signature(url_para=url_para, para_dict=payloads, site_info=site_info)
    payloads["_aop_signature"] = sig
    return url_full, payloads


def get_ali_api_parallel_para(api_namespace: str, api_name: str, site_connection_id: int, payloads: dict):
    access_token, site_info = get_access_token(site_connection_id=site_connection_id)
    # log.info(f"Got access_token: {access_token}")

    url_full, url_para = build_invoke_url_path(
        api_namespace=api_namespace, api_name=api_name, api_key=site_info.site_id
    )

    # log.info(f"Web url_full: {url_full}, url_para: {url_para}")

    payloads_new = {}
    for page_no, payload in payloads.items():
        payload["access_token"] = access_token

        sig = get_alibaba_api_signature(url_para=url_para, para_dict=payload, site_info=site_info)
        payload["_aop_signature"] = sig

        payloads_new[page_no] = payload

    return url_full, payloads_new


def handle_ali_vendor_product_ids(product_details):
    new_products = []
    for p in product_details:
        new_product = {}
        images = p.get("productImage", {}).get("images")

        new_product["imageUrl"] = images[0] if images else None
        new_product["subject"] = p.get("subject")
        new_product["subjectTrans"] = p.get("subjectTrans")
        new_product["offerId"] = p.get("offerId")
        new_product["isJxhy"] = p.get("isJxhy")

        price_range_list = p.get("productSaleInfo", {}).get("priceRangeList")
        if not price_range_list:
            break
        new_product["priceInfo"] = {"price": price_range_list[0].get("price")}

        new_product["offerIdentities"] = p.get("offerIdentities")
        new_product["tradeScore"] = p.get("tradeScore")

        new_product["topCategoryId"] = p.get("topCategoryId")
        new_product["secondCategoryId"] = p.get("secondCategoryId")
        new_product["thirdCategoryId"] = p.get("thirdCategoryId")
        new_product["minOrderQuantity"] = p.get("minOrderQuantity")
        new_product["createDate"] = p.get("createDate")
        new_products.append(new_product)
    return new_products


def handle_ali_api_product_result(result) -> dict | None:
    if result:
        ret = result.get("result") if (type(result) is dict) else result
        if (type(ret) is dict) and ("result" in ret):
            return ret.get("result")

        return ret
    else:
        return None


async def query_top_list_products(
        site_connection_id: int,
        category_id: int | None = None,
        rank_type: str = "complex",
        limit: int = 20,
        language: str = "en"
):
    """
    rankId	java.lang.String	是	榜单ID，可传入类目ID，目前支持类目榜单	1111
    rankType	java.lang.String	是	榜单类型，complex综合榜，hot热卖榜，goodPrice好价榜	complex
    limit	java.lang.Integer	是	榜单商品个数，最多20	10
    language	java.lang.String	是	榜单商品语言	en
    """
    api_name = "product.topList.query"

    para_dict = {
        "rankQueryParams": json.dumps({
            "rankId": category_id,
            "rankType": rank_type,
            "limit": limit,
            "language": language,
        }).replace(" ", "")
    }

    # log.info(f"para_dict: {para_dict}")

    products = handle_ali_api_product_result(await ali_api_call(
        api_namespace=api_namespace_product,
        api_name=api_name,
        site_connection_id=site_connection_id,
        payload=para_dict,
        method='POST'
    ))

    return products


async def query_top_list_products_parallel(
        site_connection_id: int,
        category_ids: list[int],
        rank_type: str = "complex",
        limit: int = 20,
        language: str = "en"
):
    api_name = "product.topList.query"

    def get_para_dict(category_id):
        return {
            "rankQueryParams": json.dumps({
                "rankId": category_id,
                "rankType": rank_type,
                "limit": limit,
                "language": language,
            }).replace(" ", "")
        }

    payload_list_dict = {i: get_para_dict(i) for i in category_ids}

    def _response_products(category_id, product_results: dict):
        return {
            "category_id": category_id,
            "products": product_results.get("result", {}).get("result", {}).get("rankProductModels")
        }

    product_info_dict = await ali_api_call_parallel(
        api_namespace=api_namespace_product,
        api_name=api_name,
        site_connection_id=site_connection_id,
        payloads=payload_list_dict,
        result_hook_func=_response_products,
        is_dict_result=True,
        method='POST'
    )

    all_products_nested_list = [v.get("products") for k, v in product_info_dict.items()]
    return [item for sublist in all_products_nested_list for item in sublist]

def _parse_response_products(_http_response):
    if _http_response and isinstance(_http_response, str):
        result_json = json.loads(_http_response)
        if result_json.get("error_message"):
            log.error(f"aiohttp_call result: {result_json}")
            raise AppValidationError(message=result_json.get("error_message"))

    if _http_response and isinstance(_http_response, dict) and (_http_response.get("result", {}).get('success') is False):
        log.error(f"aiohttp_call result: {_http_response.get('result')}")
        raise AppValidationError(message=_http_response.get("result", {}).get("message"))

async def ali_api_call(api_namespace: str, api_name: str, site_connection_id: int, payload: dict, method="GET"):
    log.info("start ali api call")
    full_url, payload_new = get_ali_api_call_para(
        api_namespace=api_namespace,
        api_name=api_name,
        site_connection_id=site_connection_id,
        payload=payload,
        method=method,
    )
    result = await aiohttp_call(full_url, payload=payload_new, method=method, headers=ali_header)

    _parse_response_products(result)
    return result


def get_payload(index: int, payload: dict):
    payload["beginPage"] = index
    return {"offerQueryParam": json.dumps(payload).replace(" ", "")}


def _handle_response_products(page_no, product_results: dict):
    return {"page_no": page_no, "products": product_results.get("result", {}).get("result", {}).get("data")}


async def ali_api_call_parallel(
        api_namespace: str, api_name: str, site_connection_id: int,
        payloads: dict, method="GET", result_hook_func=None, is_dict_result=True
):
    full_url, payloads_new = get_ali_api_parallel_para(
        api_namespace=api_namespace, api_name=api_name, site_connection_id=site_connection_id, payloads=payloads
    )
    http_client = AsyncHttpRequest()

    product_info_dict = await http_client.run(
        url=full_url,
        payloads=payloads_new,
        method=method,
        header=ali_header,
        response_parse_method=result_hook_func,
        is_dict_result=is_dict_result,
    )

    return product_info_dict


#
# async def query_product_info_parallel(
#         org_id: int,
#         keyword: str,
#         site_connection_id: int,
#         begin_page: int = 1,
#         end_page: int = 40,
#         product_filter: str = None,
#         price_start: str = None,
#         price_end: str = None,
#         category_id: int = None,
#         language: str = "en",
#         month_sold_threshold: int = 300,
# ):
#     api_namespace = "com.alibaba.fenxiao.crossborder"
#     api_name = "product.search.keywordQuery"
#
#     page_size = 30
#
#     offer_query_param = {
#         "keyword": keyword,
#         "pageSize": page_size,
#         "filter": product_filter,
#         "outMemberId": None,
#         "priceStart": price_start,
#         "priceEnd": price_end,
#         "categoryId": category_id,
#         "country": language,
#     }
#     offer_query_param = {k: v for k, v in offer_query_param.items() if v}
#
#     payload_list_dict = {i: get_payload(index=i, payload=offer_query_param) for i in range(begin_page, end_page + 1)}
#
#     product_info_dict = await ali_api_call_parallel(
#         api_namespace=api_namespace,
#         api_name=api_name,
#         site_connection_id=site_connection_id,
#         payloads=payload_list_dict,
#         result_hook_func=_handle_response_products,
#         is_dict_result=True
#     )
#     product_info_list = list(product_info_dict.values())
#     product_count = len(product_info_list)
#
#     if product_count == 0:
#         log.info("There is no product in the query result!")
#         return [], None
#
#     all_products = [p for i in product_info_list if i.get("products") for p in i.get("products")]
#
#     product_all_count = len(all_products)
#
#     log.info(f">>> There are {product_all_count} products in the query result before filtering!")
#
#     filtered_products = [
#         p
#         for p in all_products
#         if (
#                 (p.get("monthSold") >= month_sold_threshold) and
#                 (not any([n in p.get("subject") for n in blacklisted_products_names])) and
#                 (p.get("offerId") not in blacklisted_products_ids)
#         )
#     ]
#
#     product_count = len(filtered_products)
#
#     if product_count == 0:
#         log.info("There is no product in the query result!")
#         return [], None
#
#     additional_name = f"_{category_id}" if category_id else {f"_{keyword}" if keyword else ""}
#     vendor_product_info_file_path = f"{vendor_data_path}/ali_1688_product_info_{org_id}{additional_name}.json"
#     write_json_file(filtered_products, vendor_product_info_file_path)
#     log.info(f"Write {product_count} product info into {vendor_product_info_file_path}")
#
#     return filtered_products, vendor_product_info_file_path


def get_1688_product_detail_payload(offer_id: int, language: str = "en"):
    offer_query_param = {"offerId": offer_id, "country": language}

    return {"offerDetailParam": json.dumps(offer_query_param).replace(" ", "")}


async def query_product_details_parallel(
        payload_list_dict: dict,
        site_connection_id: int,
        is_dict_result: bool = False,
):
    api_namespace = "com.alibaba.fenxiao.crossborder"
    api_name = "product.search.queryProductDetail"

    product_detail_dict = await ali_api_call_parallel(
        api_namespace=api_namespace,
        api_name=api_name,
        site_connection_id=site_connection_id,
        payloads=payload_list_dict,
        method="POST",
    )

    if not product_detail_dict:
        log.info("There is no product detail in the query result!")
        return []

    # Check first result to ensure there is no issue for the calling params
    first_product_detail = product_detail_dict.get(list(product_detail_dict.keys())[0])
    _parse_response_products(first_product_detail)

    product_details = {k: p.get("result", {}).get("result") for k, p in product_detail_dict.items() if p}

    if is_dict_result:
        product_details_return = product_details
    else:
        product_details_return = list(product_details.values())

    product_count = len(product_details_return)

    if product_count == 0:
        log.info("There is no product detail in the query result!")
        return []

    return product_details_return


isc_categories_file_path = "supplier/isc_categories/ali_1688_category/isc_categories.json"


def read_all_category_ids(level: int = 2):
    with open(isc_categories_file_path, "r") as file:
        category_info_json = file.read()

    category_info = json.loads(category_info_json)
    all_categories = category_info.get("category_list")
    log.info(f"Length of all categories: {len(all_categories)}")

    filtered_categories = [c.get("category_id") for c in all_categories if c.get("level") == str(level)]
    log.info(f"Length of filtered categories: {len(filtered_categories)} with level {level}")
    return filtered_categories


def read_category_product_details(org_id: int, category_ids: list[int] | None = None, keyword: str | None = None):
    # Create a dictionary mapping category_id to file
    product_details_paths = {}

    if category_ids:
        for category_id in category_ids:
            file_path = f"{vendor_data_path}/ali_1688_product_details_{org_id}_{category_id}.json"
            product_details_paths[category_id] = file_path
    elif keyword:
        file_path = f"{vendor_data_path}/ali_1688_product_details_{org_id}_{keyword}.json"
        product_details_paths[keyword] = file_path
    else:
        file_paths = os.listdir(vendor_data_path)

        # Filter files that start with 'ali_1688_product_details_1_'
        filtered_files = [
            f"{vendor_data_path}/{file}"
            for file in file_paths
            if file.startswith(f"ali_1688_product_details_{org_id}_")
        ]

        for file in filtered_files:
            # Extract category_id from the file name
            key = int(file.split("_")[-1].split(".")[0])

            # Map category_id to the file
            product_details_paths[key] = file

    category_product_details = {}
    for key, path in product_details_paths.items():
        if os.path.exists(path):
            with open(path, "r") as file:
                # Load the JSON data from the file
                data = json.load(file)
                log.info(f"Loaded product details json file from {path}")
                category_product_details[key] = data
        else:
            log.warning(f"The product details file path {path} does not exist!")

    # log.info(f'> category_product_details:\n{category_product_details}')

    product_count = len(category_product_details.values())
    return category_product_details, product_count


async def get_product_details_with_item_ids(site_connection_id: int, item_ids: list[int]):
    if not item_ids:
        log.info("No product ID to query!")
        return []

    payload_list_dict = {
        p: get_1688_product_detail_payload(offer_id=p)
        for p in item_ids
    }

    product_details = await query_product_details_parallel(
        payload_list_dict=payload_list_dict, site_connection_id=site_connection_id
    )

    return product_details


async def get_ali_product_details(offer_id: int, site_connection_id: int, language: str = "en"):
    log.info(f"offer_id: {offer_id}, language: {language}, site_connection_id {site_connection_id}")
    # plan_checker(plan_info)

    api_name = "product.search.queryProductDetail"
    offer_query_param = {
        "offerId": offer_id,
        "country": language
    }

    para_dict = {
        "offerDetailParam": json.dumps(offer_query_param).replace(" ", "")
    }
    # log.info(f"para_dict: {para_dict}")

    data = handle_ali_api_product_result(await ali_api_call(
        api_namespace=api_namespace_product,
        api_name=api_name,
        site_connection_id=site_connection_id,
        payload=para_dict,
        method="POST"
    ))

    return data


async def call_ali_api(offer_query_param, api_name, site_connection_id, country='en', method="POST"):
    offer_query_param["country"] = country

    offer_query_param = {k: v for k, v in offer_query_param.items() if v}
    # offer_query_param['keywordTranslate'] = True
    para_dict = {
        "offerQueryParam": json.dumps(offer_query_param).replace(" ", "")
    }

    # log.info(f"para_dict: {para_dict}")
    result = handle_ali_api_product_result(await ali_api_call(
        api_namespace=api_namespace_product,
        api_name=api_name,
        site_connection_id=site_connection_id,
        payload=para_dict,
        method=method
    ))

    return result


def get_product_base_price(product):
    return product.get("productSaleInfo", {}).get("consignPrice") or \
        product.get("productSaleInfo", {}).get("priceRangeList", [{}])[0].get("price")


def get_shipping_info(product, raw_product: dict | None = None, is_service=False):
    shipping_info = product.get(
        "productShippingInfo", {}
    ).get("shippingTimeGuarantee")

    if is_service:
        services = raw_product.get("serviceList")
        shipping_info = shipping_info or (services[0] if services else None)

    return "Ships within 48 hours" if (
            shipping_info and shipping_info in ['shipIn48Hours', 'sendGoods48H']
    ) else shipping_info


def get_ali_category_str(product: dict):
    categories = [
        str(product.get(level)) for level in ("topCategoryId", "secondCategoryId", "thirdCategoryId")
        if product.get(level)
    ]
    return " > ".join(categories)

def remove_skipped_products(
        products: list[dict], title_zh_key: str = "subject"
):
    return [
        p for p in products
        if (p and (not any([n in p.get(title_zh_key) for n in skipped_product_keywords])))
    ]

def check_skipped_product(product: dict, title_zh_key: str = "subject"):
    return any([n in product.get(title_zh_key) for n in skipped_product_keywords])
from db.pg_site_connection import get_website_info, get_website_role_type, get_site_connection_info
from db.pg_site_provider_info import get_site_provider_info_by_id
from ecommerce.order import order_service
from ecommerce.order.adapters.shop_store.order_shop_store_shopify_adapter import OrderShopStoreShopifyAdapter
from utility.log import get_logger

log = get_logger(__name__)


class OrderOrchestrator:
    _store_adapter = {
        "Shopify": OrderShopStoreShopifyAdapter
    }

    def __init__(
            self
    ):
        self.store_adapter = None

    @staticmethod
    def get_site_provider_info(site_connection_id: int):
        website_info = get_website_info(site_connection_id)
        assert website_info, "Failed to get website info"
        site_type = website_info.site_type
        site_provider_info = get_site_provider_info_by_id(site_type)
        assert site_provider_info, "Failed to get site provider info"
        role_type = get_website_role_type(site_connection_id)
        return website_info, site_provider_info, role_type

    async def init_store_adapter(self, target_site_connection_id: int):
        connection_info = get_site_connection_info(target_site_connection_id)
        website_info, site_provider_info, role_type = self.get_site_provider_info(
            target_site_connection_id
        )
        issuer = site_provider_info.get("issuer")
        store_adapter_class = self._store_adapter.get(issuer)
        assert store_adapter_class, f"Adapter not found for {issuer}"
        self.store_adapter = store_adapter_class(
            target_site_connection_id, connection_info.org_id, connection_info.user_id
        )
        self.store_adapter.init(website_info, site_provider_info, role_type)

    async def create_shop_order(self, site_connection_id: int, order_data):
        await self.init_store_adapter(site_connection_id)
        order_data = self.store_adapter.convert_shop_order_data(order_data)
        old_order = await order_service.get_shop_order_by_id(
            order_data.get("order_id")
        )
        if old_order:
            order_data.update({"id": old_order.id})
        new_order = await order_service.save_shop_main_order(order_data)

        if new_order:
            shop_order_id = new_order.id
            order_lines = self.store_adapter.convert_shop_order_line_items(order_data.get("products_raw"))
            order_lines = [{"order_id": shop_order_id, **line} for line in order_lines]
            await order_service.save_shop_order_line_items(shop_order_id, order_lines)

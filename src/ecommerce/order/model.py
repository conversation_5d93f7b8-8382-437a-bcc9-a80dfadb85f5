from __future__ import annotations

from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, TypeVar, Generic

from pydantic import BaseModel

from ecommerce.product.common_api import CreateProduct, AIOrderStatus

T = TypeVar("T")


class CreateOrderInput(BaseModel):
    order_id: str
    products: list[CreateProduct]


class OrderInput(BaseModel):
    order_id: str
    line_items: list[dict]
    status: str
    carrier_tracking_ref: str | None = None


class SaleOrderInput(BaseModel):
    order_id: str
    status: str
    carrier_tracking_ref: str | None = None


class OrderStatus(BaseModel):
    status: AIOrderStatus


class FulfillmentOrderStatus(Enum):
    unpaid = 1  # 客户下单但未付款。
    paid = 2  # 客户付款，准备处理订单。
    partially_dispatched = 3  # 订单中的部分商品已发货，对应的包裹状态为 Dispatched。
    dispatched = 4  # 全部发货 订单已发货
    completed = 5  # 所有包裹送达，订单状态更新为 Completed。
    partially_returned = 6  # 客户退回部分商品，对应的包裹状态为 Returned。
    returned = 7  # 客户退回所有商品，对应的包裹状态为 Returned。
    return_delivered = 8  # 退回的包裹已到达退货地点。
    return_completed = 9  # 所有退货处理完成，订单状态更新为 Return Completed
    # canceled


class ParcelStatus(Enum):
    dispatched = 1
    delivered = 2
    returned = 3


class FulfillmentOrderInput(BaseModel):
    fulfillment_order_id: str
    line_items: list[dict]
    status: ParcelStatus
    carrier_tracking_ref: str | None = None


class SellerFulfillmentOrderInput(BaseModel):
    line_items: list[SimpleLineItem]
    company: str
    tracking_number: str


class SellerFulfillmentOrders(BaseModel):
    order_items: list[SellerFulfillmentOrderInput]


class WooOrderParam(BaseModel):
    page: int
    per_page: int
    before: str | None
    after: str | None
    include: list[str] | None
    search: str | None


class OrderResult(BaseModel):
    orders: list[Order | None]
    total_pages: int
    total_count: int


class LineItem(BaseModel):
    id: str
    name: str
    price: str | None
    sku: str | None
    quantity: int
    image: str | None
    product_handle: str | None


class ShopLineItem(BaseModel):
    id: str
    sku: str | None
    quantity: int
    price: float
    image: str | None
    attr: str | None
    warehouse: str | None


class PriceInfo(BaseModel):
    total_price: float
    total_discount: float
    total_tax: float
    total_shipping: float
    currency: str


class Shipping(BaseModel):
    address: str | None
    first_name: str
    last_name: str
    receiver_name: str
    phone: str | None


class ShopShipping(BaseModel):
    name: str  # 收货人
    phone: str | None  # 联系方式
    address: str | None  # 收货地址


class TimeInfo(BaseModel):
    order_created: str | None
    latest_delivery_time: str | None
    delivery_time: str | None
    estimated_delivery_time: str | None


class Packages(BaseModel):
    id: str
    company: str
    tracking_number: str
    quantity: int
    status: str


class FulfillmentInfo(BaseModel):
    fulfillment_method: str | None
    package_number: str | None


class Order(BaseModel):
    id: str
    user_id: str
    name: str
    status: str
    shipping: Shipping | None
    products: list[LineItem]
    total_price: str
    total_discount: str
    total_shipping: str
    total_tax: str
    payment_method: str | None
    date_paid: str | None
    currency: str


class AiOrder(BaseModel):
    order_id: str
    user_id: str
    name: str
    raw_order_status: str
    shipping: Shipping | None
    products: list[LineItem]
    total_price: str
    total_discount: str
    total_shipping: str
    total_tax: str
    payment_method: str | None
    date_paid: str | None
    currency: str
    site_id: int
    org_id: int
    order_status: str
    sync_time: str


class ShopOrder(BaseModel):
    site_id: int
    org_id: int
    order_id: str
    name: str
    order_status: str
    shipping: ShopShipping | None
    products: list[ShopLineItem]
    time_info: TimeInfo
    price_info: PriceInfo
    fulfillment_info: FulfillmentInfo


class DeliveryContact(BaseModel):
    name: str
    email: str
    mobile: str
    phone: str
    zip: str
    country_code: str
    state_name: str
    city: str
    street: str
    street2: str
    ref: str


class OrderLines(BaseModel):
    wd_variant_id: str
    quantity: int


class SimpleLineItem(BaseModel):
    sku: str
    quantity: int
    price: float | None


class AiOrderInput(BaseModel):
    order_id: str | None
    platform_type: int
    order_type: int
    site_id: int
    shipping: DeliveryContact | None
    line_items: list[SimpleLineItem]


class OrderData(BaseModel):
    delivery_contact: DeliveryContact
    platform_type: int
    order_lines: list[OrderLines]
    client_order_ref: str


class OrderDeliveryInput(BaseModel):
    site_connection_id: int
    order_type: int
    order_id: str
    platform_type: int


class PaidOrderInput(BaseModel):
    order_id: str


class FulfillmentOrder(BaseModel):
    id: str
    order_id: str
    fulfillment_id: str
    products: LineItem
    status: str
    order_type: str
    created_time: str
    update_time: str
    packages: Packages | None
    shipping: Shipping | None


class DownloadPackingSlipPara(BaseModel):
    order_ids: list[int]


class OrderSettings(BaseModel):
    id: int
    sync_type: int


class OrderLineItemModel(BaseModel):
    product_id: str
    name: str
    quantity: int
    unit_price: float
    total_price: float
    product_details: Optional[Dict] = None


class OrderModel(BaseModel):
    order_id: str
    user_id: str
    name: str

    # Status tracking
    raw_order_status: str
    order_status: str

    # Financial details
    total_price: float
    total_discount: float
    total_shipping: float
    total_tax: float

    # Payment information
    payment_method: str
    date_paid: str
    currency: str

    # Metadata
    site_id: str
    org_id: int
    platform_type: int
    sync_time: str

    # Optional fields
    shipping: Optional[Dict] = None
    meta_data: Optional[Dict] = None
    line_items: List[OrderLineItemModel]


def create_order_model(data: dict) -> OrderModel:
    return OrderModel(**data)


class UpdateOrderStatus(BaseModel):
    wd_order_id: int
    order_type: str | None = None
    # site_info_id: int
    order_status: AIOrderStatus


class ListOrderInput(BaseModel):
    page_limit: int = 10
    page: int | None = 1
    sku_id: str | None = None
    spu_id: str | None = None
    order_status: list[AIOrderStatus] | None = None
    order_number: str | None = None
    created_time_start: str | None = None
    created_time_end: str | None = None
    latest_delivery_time: str | None = None
    order: dict | None = None  # {"order_number":"desc"}


class OrderLineItem(BaseModel):
    product_id: str  # 货品product id
    variant_id: str  # 货品variant id
    wd_variant_id: str | None = None  # wd_variant_id
    wd_product_id: str | None = None
    spu: str | None = None  # 货品spu id
    sku: str | None  # 货品 sku id
    attributes: list  # 属性字符串，格式是Color: Blue, Size: Large
    warehouse: str | None = None  # 仓库
    price: str  # 订单产品价格
    quantity: float  # 订单产品数量
    name: str  # 货品名称
    image: str  # 货品图片


class SubOrderItem(BaseModel):
    id: int
    site_provider_id: int | None = None  # 供应商编号
    sub_order_number: str
    warehouse: str
    line_items: list[OrderLineItem]
    status: AIOrderStatus
    created_time: str
    updated_time: str


class SubOrderItemOutput(BaseModel):
    id: int
    order_type: str
    order_name: str
    site_name: str | None = None
    country: str | None = None
    order_status: int | None = None
    line_items: List[Dict] | None = None  # Assuming line_items is a list of dictionaries
    receiver: str | None = None
    contact: str | None = None
    shipping_address: str | None = None
    created_time: datetime
    latest_delivery_time: Optional[datetime] = None
    delivery_time: Optional[datetime] = None
    estimated_time: Optional[datetime] = None
    delivery_method: str | None = None
    tracking_number: Optional[str] = None
    # SubOrderItem specific fields
    sub_order_number: str
    warehouse: Optional[str] = None
    supplier_info: Optional[Dict] = None


class OrderItem(BaseModel):
    id: int  # 订单id
    is_sync: bool  # 订单同步状态
    order_name: str  # 订单名
    site_provider_id: int | None = None  # 供应商编号
    site_name: str | None = None  # 站点名
    country: str | None = None  # 国家
    order_status: AIOrderStatus  # 订单状态
    line_items: list[OrderLineItem]  # 订单产品
    receiver: str | None = None  # 收货人
    contact: str | None = None  # 联系方式
    shipping_address: str | None = None  # 收货地址
    created_time: str  # 创建时间
    latest_delivery_time: str | None = None  # 最晚发货时间
    delivery_time: str | None = None  # 实际发货时间
    estimated_time: str | None = None  # 预计送达时间
    delivery_method: str | None = None  # 发货方式
    tracking_number: str | None = None  # 包裹号
    sub_orders: list[SubOrderItem] | None = None


class OrderItem(BaseModel):
    id: int  # 订单id
    is_sync: bool  # 订单同步状态
    order_type: str = "order"
    order_name: str  # 订单名
    site_name: str | None = None  # 站点名
    country: str | None = None  # 国家
    order_status: AIOrderStatus  # 订单状态
    line_items: list[OrderLineItem]  # 订单产品
    receiver: str | None = None  # 收货人
    contact: str | None = None  # 联系方式
    shipping_address: str | None = None  # 收货地址
    created_time: str  # 创建时间
    latest_delivery_time: str | None = None  # 最晚发货时间
    delivery_time: str | None = None  # 实际发货时间
    estimated_time: str | None = None  # 预计送达时间
    delivery_method: str | None = None  # 发货方式
    tracking_number: str | None = None  # 包裹号
    sub_orders: list[SubOrderItemOutput] | None = None


class SyncOrders(BaseModel):
    synced_order_ids: list[int]


class SyncOrdersStatus(BaseModel):
    synced_order_ids: list[int]
    is_sync: bool


class Response(BaseModel, Generic[T]):
    data: T
    msg: str
    status: bool


class PaginationResponseData(BaseModel, Generic[T]):
    """
    Generic Pagination Response Data Model

    Allows creating type-specific pagination responses

    Type Parameters:
    - T: The type of data items in the list
    """
    data: List[T]
    total_count: int
    total_pages: int


class OrderPaginationResponseData(PaginationResponseData[OrderItem]):
    pass


class OrderPaginationResponse(Response[OrderPaginationResponseData]):
    pass


class WooOrderStatusState(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    ON_HOLD = "on-hold"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    REFUNDED = "refunded"
    FAILED = "failed"

    # Additional standard statuses
    CHECKOUT_DRAFT = "checkout-draft"  # Draft order created during checkout
    TRASH = "trash"  # Order moved to trash


class SubOrderType(str, Enum):
    ISC = "isc"
    Woo = "woo"
    Alibaba = "1688"


class SubOrderInput(BaseModel):
    order_type: SubOrderType
    product_id: str
    warehouse: str | None = None
    supplier_info: dict | None = None
    source_site_connection_id: int | None = None

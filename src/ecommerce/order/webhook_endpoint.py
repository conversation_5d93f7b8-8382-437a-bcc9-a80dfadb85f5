from __future__ import annotations

import json

from fastapi import BackgroundTasks, API<PERSON><PERSON><PERSON>
from starlette.requests import Request

from db.pg_order import check_order_status
from db.pg_order_settings import PGOrderSettings
from db.pg_site_connection import get_website_info
from ecommerce import factory
from ecommerce.order import order_api
from ecommerce.order.isc_order import create_isc_order, update_order, sync_woocommerce_order
from ecommerce.order.model import WooOrderStatusState
from ecommerce.order.order_orchestrator import OrderOrchestrator
from ecommerce.shopify import webhook_mapping
from utility.api_helper import success_response_handler
from utility.log import get_logger

router_order_webhook = APIRouter(prefix="/shop_product", tags=["EC Product Common"])
log = get_logger(__name__)


@router_order_webhook.post("/create-shopify-order-webhook/{site_connection_id}/{org_id}")
async def create_order_webhook(
        background_tasks: BackgroundTasks,
        request: Request,
        site_connection_id,
        org_id
):
    json_data = await request.json()
    order_data = webhook_mapping.create_webhook_create_order_item(json_data)
    log.info(f"webhook data is {order_data}, site_id is {site_connection_id} and org_id is {org_id}")
    background_tasks.add_task(order_api.sync_ai_orders, site_connection_id, org_id, 100)
    return success_response_handler(data=None, msg="create order from webhook")


@router_order_webhook.post("/create-order-webhook/{site_connection_id}")
async def create_order_webhook(
        request: Request,
        site_connection_id
):
    json_data = await request.json()
    await OrderOrchestrator().create_shop_order(site_connection_id, json_data)
    return success_response_handler(data=None, msg="create order from webhook")


def sync_order_status(woo_order_id, site_connection_id, site_id):
    woo_client = factory.get_eshop(site_connection_id=site_connection_id)
    # update ai order status
    woo_order = woo_client.get_order(woo_order_id)
    order = sync_woocommerce_order(woo_order_id, woo_order, site_connection_id, site_id, None)
    return order


def process_order_created_webhook(webhook_order_status: WooOrderStatusState, woo_order_id, site_connection_id, site_id):
    webhook_process_data = {
        WooOrderStatusState.PENDING: sync_order_status,
        WooOrderStatusState.CHECKOUT_DRAFT: sync_order_status,
        WooOrderStatusState.ON_HOLD: sync_order_status,
        WooOrderStatusState.PROCESSING: sync_order_status,
    }
    if webhook_process_data.get(webhook_order_status):
        log.info(f"webhook order status is {webhook_order_status}, sync order {woo_order_id} to nuwa")
        webhook_process_data[webhook_order_status](woo_order_id, site_connection_id, site_id)
        return True
    else:
        log.info(f"webhook status {webhook_order_status} not support")
        return False


def update_webhook_order_status(webhook_data, updated_order_id, order_status, site_connection_id, site_id):
    if check_order_status(updated_order_id, site_id):
        log.info("order is comfirmed, skip the order")
        return False
    else:
        woo_client = factory.get_eshop(site_connection_id=site_connection_id)

        result = update_order(woo_client, updated_order_id, webhook_data, order_status)
        if result:
            # update ai order status
            # log.info(f"create isc order {updated_order_id} successful")
            # PROCESSING = 2 # 2 is processing
            # woo_order_status = PROCESSING
            # log.info(f"update order processing status for order {updated_order_id}")
            # update_order_status(updated_order_id, woo_order_status)
            return True
        else:
            return False


def process_order_updated_webhook(
        webhook_order_status: WooOrderStatusState, webhook_data, updated_order_id, site_connection_id, site_id):
    webhook_process_data = {
        WooOrderStatusState.PROCESSING: update_webhook_order_status,
        WooOrderStatusState.CANCELLED: update_webhook_order_status,
        WooOrderStatusState.REFUNDED: update_webhook_order_status,
        WooOrderStatusState.COMPLETED: update_webhook_order_status,
        WooOrderStatusState.ON_HOLD: update_webhook_order_status,
        WooOrderStatusState.FAILED: update_webhook_order_status,
    }

    if webhook_process_data.get(webhook_order_status):
        log.info(f"webhook order status is {webhook_order_status}, update nuwa order {updated_order_id}")
        webhook_process_data[webhook_order_status](
            webhook_data, updated_order_id, webhook_order_status, site_connection_id, site_id)
        return True
    else:
        log.info(f"webhook status {webhook_order_status} not support")
        return False


@router_order_webhook.post("/add-order-create-webhook/{site_connection_id}")
async def add_order_create_webhook(
        # background_tasks: BackgroundTasks,
        request: Request,
        site_connection_id,
):
    if site_connection_id:
        site_info = get_website_info(site_connection_id)
        site_id = site_info.id
    else:
        site_id = None
    pg_order_setting = PGOrderSettings.get_order_settings(site_id)
    if pg_order_setting and pg_order_setting.get("sync_type") == 0:
        log.info("use manual sync, ignore webhook")
        return success_response_handler(data=None, msg="use manual sync, ignore webhook")
    json_data = await request.body()
    fix_bytes_value = json_data.replace(b"'", b'"')
    try:
        webhook_data = json.loads(fix_bytes_value.decode("utf-8"))
    except Exception as e:
        return success_response_handler(data=None, msg="ignore webhook")
    try:
        webhook_order_status = webhook_data.get("status")
        woo_order_id = webhook_data.get("id")
        log.info(f"receive order created webhook, status is {webhook_order_status}")
        if webhook_order_status:
            webhook_order_status = WooOrderStatusState(webhook_order_status)
            result = process_order_created_webhook(webhook_order_status, woo_order_id, site_connection_id, site_id)
            if result:
                return success_response_handler(data=None, msg="process order created webhook successful")
            else:
                return success_response_handler(data=None, msg="process order created webhook failed")
        else:
            return success_response_handler(data=None, msg="ignore webhook")
    except Exception as e:
        log.info(f"error is {str(e)}")
        return success_response_handler(data=None, msg="invalid webhook data")


@router_order_webhook.post("/add-order-update-webhook/{site_connection_id}")
async def add_order_update_webhook(
        # background_tasks: BackgroundTasks,
        request: Request,
        site_connection_id,
):
    if site_connection_id:
        site_info = get_website_info(site_connection_id)
        site_id = site_info.id
    else:
        site_id = None
    pg_order_setting = PGOrderSettings.get_order_settings(site_id)
    if pg_order_setting and pg_order_setting.get("sync_type") == 0:
        log.info("use manual sync, ignore webhook")
        return
    log.info(f"request is {request.body()}")
    try:
        json_data = await request.json()
    except Exception as e:
        log.info(f"ignore useless webhook, error is {str(e)}")
        return
    # fix_bytes_value = json_data.replace(b"'", b'"')
    # try:
    # webhook_data = json.loads(fix_bytes_value.decode("utf-8"))
    # except Exception as e:
    # log.info(f"ignore useless webhook data: {json_data}, error is {str(e)}")
    # return
    webhook_data = json_data
    webhook_order_status = webhook_data.get("status")
    updated_order_id = webhook_data.get("id")
    try:
        webhook_order_status = WooOrderStatusState(webhook_order_status)
        result = process_order_updated_webhook(
            webhook_order_status, webhook_data, updated_order_id, site_connection_id, site_id)

        if result:
            # background_tasks.add_task(order_api.sync_ai_orders, site_connection_id, org_id, 100)
            return success_response_handler(data=None, msg="update isc order from webhook")
        else:
            return success_response_handler(data=None, msg="update isc order from webhook failed")
    except Exception as e:
        log.info(f"update order failed: {str(e)}")
        return success_response_handler(data=None, msg="invalid webhook data")


@router_order_webhook.post("/create-paid-order-webhook/{site_connection_id}")
async def create_woo_order_webhook(
        # background_tasks: BackgroundTasks,
        request: Request,
        site_connection_id,
):
    log.info(request)
    json_data = await request.body()
    log.info(json_data)
    fix_bytes_value = json_data.replace(b"'", b'"')
    log.info(fix_bytes_value)
    try:
        json_data = json.loads(fix_bytes_value.decode("utf-8"))
        log.info(f"json_data is {json_data}")
        sale_orders = json_data
        woo_client = factory.get_eshop(site_connection_id=site_connection_id)
        log.info("create isc order")
        result = create_isc_order(woo_client.site_info_id, woo_client.wc_api, woo_client.site_connection_id,
                                  sale_orders)
        log.info("create isc order finish")
        if result:
            # background_tasks.add_task(order_api.sync_ai_orders, site_connection_id, org_id, 100)
            return success_response_handler(data=None, msg="create isc order from webhook successful")
        else:
            return success_response_handler(data=None, msg="create isc order from webhook failed")
    except Exception as e:
        log.info(f"error is {str(e)}")
        return success_response_handler(data=None, msg="invalid webhook data")


@router_order_webhook.post("/create-woo-order-webhook/{site_connection_id}/{org_id}")
async def create_woo_order_data_webhook(
        background_tasks: BackgroundTasks,
        request: Request,
        site_connection_id,
        org_id
):
    log.info(request)
    json_data = await request.body()
    log.info(json_data)
    fix_bytes_value = json_data.replace(b"'", b'"')
    log.info(fix_bytes_value)
    background_tasks.add_task(order_api.sync_ai_orders, site_connection_id, org_id, 100)
    return success_response_handler(data=None, msg="create order from webhook")

# @router_order_webhook.post("/setup-shopify-create-order-webhook")
# def setup_create_order_webhook(
#         site_connection_id: str = Header(...),
#         decoded_token=Depends(authenticated_user)
# ):
#     org_id = decoded_token.get('org_id')
#     webhook_url = "https://nuwa-stg.warpdriven.ai/latest/shop_product/create-shopify-order-webhook"
#     callback_url = f"{webhook_url}/{site_connection_id}/{org_id}"
#     webhook_data = webhook_api.create_order_webhook(site_connection_id,
#                                                     callback_url)
#     if webhook_data:
#         return success_response_handler(data=None, msg="create order from webhook")
#     return error_response_handler(400, "Set webhook error", "")
#
#
# @router_order_webhook.post("/setup-shopify-paid-order-webhook")
# def setup_paid_order_webhook(
#         site_connection_id: str = Header(...),
#         _decoded_token=Depends(authenticated_user)
# ):
#     callback_url = f"https://nuwa-stg.warpdriven.ai/latest/shop_product/set-order-paid/{site_connection_id}"
#     webhook_data = webhook_api.create_order_paid_webhook(site_connection_id,
#                                                          callback_url)
#     if webhook_data:
#         return success_response_handler(data=None, msg="create order from webhook")
#     return error_response_handler(400, "Set webhook error", "")


# @router_order_webhook.post("/setup-woo-paid-order-webhook")
# def setup_woo_paid_order_webhook(
#         site_connection_id: str = Header(...),
#         decoded_token=Depends(authenticated_user)
# ):
#     org_id = decoded_token.get('org_id')
#     callback_url = f"https://nuwa-stg.warpdriven.ai/latest/shop_product/set-order-paid/{site_connection_id}/{org_id}"
#     eshop: ECShop = factory.get_eshop(site_connection_id=site_connection_id)
#     webhook_data = eshop.create_webhook(callback_url)
#     if webhook_data:
#         return success_response_handler(data=None, msg="create order paid status from webhook")
#     return error_response_handler(400, "Set webhook error", "")

from abc import ABC

from utility.log import get_logger

log = get_logger(__name__)


class OrderAdapter(ABC):

    def __init__(self, site_connection_id, org_id, user_id):
        self.site_connection_id = site_connection_id
        self.org_id = org_id
        self.user_id = user_id
        self.identify_field = {"site_connection_id": site_connection_id, "org_id": org_id}
        self.website_info = None
        self.site_type = None
        self.site_provider_info = None
        self.platform_type = None
        self.role_type = None

    def init(self, website_info, site_provider_info, role_type):
        self.website_info = website_info
        self.site_type = website_info.site_type
        self.site_provider_info = site_provider_info
        self.platform_type = site_provider_info.get("platform_type")
        self.role_type = role_type

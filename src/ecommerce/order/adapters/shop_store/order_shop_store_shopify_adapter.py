from datetime import datetime

from ecommerce.order.adapters.shop_store.order_shop_store_adapter import OrderShopStoreAdapter
from ecommerce.order.model import Shipping, LineItem
from ecommerce.product.common_api import AIOrderStatus
from utility.log import get_logger

log = get_logger(__name__)


class OrderShopStoreShopifyAdapter(OrderShopStoreAdapter):

    def __init__(self, site_connection_id, org_id, token):
        super().__init__(site_connection_id, org_id, token)

    @staticmethod
    def get_ai_order_status(financial_status):
        if financial_status == "paid":
            return AIOrderStatus.PROCESSING.value
        else:
            return AIOrderStatus.PENDING.value

    def convert_shop_order_data(self, order_data):
        log.info(f"order data is {order_data}")
        order_status = self.get_ai_order_status(order_data.get("financial_status"))
        result = {
            "order_id": order_data.get("admin_graphql_api_id"),
            "org_id": self.org_id,
            "site_id": self.website_info.id,
            "site_connection_id": int(self.site_connection_id),
            "platform_type": self.platform_type,
            "user_id": str(self.user_id),
            "is_synchronized": True,
            "total_price": order_data.get("total_price"),
            "total_discount": order_data.get("total_discounts"),
            "total_shipping":
                order_data.get(
                    "total_shipping_price_set",
                    {"shop_money": {"amount": 0}}
                ).get("shop_money").get("amount"),
            "total_tax": order_data.get("total_tax"),
            "payment_method": order_data.get("payment_gateway_names", [""])[0],
            "payment_date": datetime.fromisoformat(order_data.get("processed_at")),
            "currency": order_data.get("currency"),
            "order_status_raw": AIOrderStatus(order_status).name,
            "order_status": order_status,
            "shipping": self.convert_shipping_data(order_data.get("shipping_address")),
            "products": self.convert_product_data(order_data.get("line_items")),
            "products_raw": order_data.get("line_items"),
            "meta_data": order_data,
            "site_provider_id": self.site_provider_info.get("id")
        }
        log.info(f"converted order data is {result}")
        return result

    @staticmethod
    def get_product_gid(product_id):
        return f"gid://shopify/Product/{product_id}"

    @staticmethod
    def get_variant_gid(variant_id):
        return f"gid://shopify/ProductVariant/{variant_id}"

    def convert_shop_order_line_items(self, line_items):
        return [
            {
                "product_id": self.get_product_gid(item.get("product_id")),
                "variant_id": self.get_variant_gid(item.get("variant_id")),
                "wd_product_id": self.get_product_gid(item.get("product_id")),
                "wd_variant_id": self.get_variant_gid(item.get("variant_id")),
                "sku": item.get("sku"),
                "name": item.get("name"),
                "quantity": item.get("quantity"),
                "price": item.get("price"),
                "image": item.get("image", ""),
                "attributes": []
            }
            for item in line_items
        ]

    @staticmethod
    def convert_product_data(product_data):
        result = []
        if product_data:
            for product in product_data:
                LineItem(
                    id=str(product["variant_id"]),
                    name=product["name"],
                    price=product["price"],
                    sku=product["sku"],
                    quantity=product["quantity"],
                    image=None,
                    product_handle=product["sku"]
                ).model_dump()
            return result

    @staticmethod
    def convert_shipping_data(param):
        if param is None:
            return {}
        shipping = Shipping(
            address=param.get("address1"),
            first_name=param.get("first_name"),
            last_name=param.get("last_name"),
            receiver_name=param.get("name"),
            phone=param.get("phone")
        ).model_dump()
        return shipping

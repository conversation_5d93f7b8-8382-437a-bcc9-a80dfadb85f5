from db.pg_async_shop_order_line_items import shop_order_line_items_crud
from db.pg_async_shop_orders import shop_order_crud
from db.transaction_manager import transactional, not_transactional


@transactional
async def save_shop_main_order(order_data, transaction_manager):
    session = transaction_manager.session
    shop_order_id = order_data.get("id")
    if shop_order_id:
        return await shop_order_crud.update_dict(session, order_data)
    else:
        return await shop_order_crud.create(session, order_data)


@transactional
async def save_shop_order_line_items(shop_order_id, order_line_items, transaction_manager):
    session = transaction_manager.session
    await shop_order_line_items_crud.delete_by_shop_order_id(session, shop_order_id)
    return await shop_order_line_items_crud.bulk_create(session, order_line_items)


@not_transactional
async def get_shop_order_by_id(order_id, transaction_manager):
    session = transaction_manager.session
    return await shop_order_crud.get_shop_order_by_order_id(session, order_id)

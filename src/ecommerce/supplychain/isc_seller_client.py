import requests
import os
import math

from ecommerce.supplychain.models import IscProductInput
from ecommerce.supplychain.isc_base_client import IscBaseClient
from ecommerce.product.common_api import IscProduct, IscVariant
from db.pg_site_info import get_site_info
from db.pg_site_provider_info import get_site_provider_info_by_id, get_auth_api_key
from utility.log import get_logger

log = get_logger(__name__)


def get_isc_url(site_id):
    result = get_site_provider_info_by_id(site_id)
    site_info = result.get("fields")
    if site_info and site_info.get("site_url"):
        return site_info.get("site_url")
    else:
        log.info(f"{site_id} site url not found")
        return None


def get_api_key(site_type):
    result = get_site_provider_info_by_id(site_type)
    site_info = result.get("fields")
    if site_info and site_info.get("service_key"):
        return site_info.get("service_key")
    else:
        log.info(f"{site_type} service key not found")
        return None


class IscSellerClient(IscBaseClient):
    def __init__(self, *, site_connection_id: int, **kwargs):
        super().__init__(site_connection_id=site_connection_id)
        #self.token = token
        self.site_connection_id = site_connection_id
        site_info = get_site_info(site_connection_id)
        if site_info:
            self.site_info_id = site_info.get("site_id")
            self.site_type = site_info.get("site_type")
        isc_url = get_isc_url(self.site_type)
        self.isc_endpoint = f"{isc_url}/isc"
        self.isc_service_endpoint = f"{isc_url}/isc_service"
        self.kwargs = kwargs

    def get_api_key(self):
        headers = {
            "X-API-Key": get_auth_api_key()
        }
        return headers

    def get_auth(self):
        auth_url = os.environ.get("AUTH_URL") or "https://auth-stg.warpdriven.ai"
        isc_auth_url = f"{auth_url}/connection/isc_client/get_token"

        auth_object = {
            "site_connection_id": self.site_connection_id,
        }
        response = requests.get(isc_auth_url, params=auth_object, headers=self.get_api_key())
        auth_token = response.json().get("data")
        return auth_token

    def get_auth_header(self):
        auth_token = self.get_auth()
        header = {"Authorization": f"Bearer {auth_token}"}
        return header

    def get_service_header(self):
        header = {"X-API-Key": get_api_key(self.site_type)}
        return header


    async def get_isc_product(self, product_id):
        auth_header = self.get_auth_header()
        products_url = f"{self.isc_endpoint}/get_product_detail"
        params = {
            "product_id": product_id,
            "role": "seller",
        }
        response = requests.get(products_url, params=params, headers=auth_header).json()
        result = response.get("data")
        return result

    def get_variant_count(self):
        auth = self.get_auth()
        return

    async def search_collections(self, search: str = None, site_type: str = "platform"):
        url = f"{self.isc_endpoint}/get_product_category_list"
        headers = self.get_auth_header()
        data = {
            "keyword": search
        }
        response = requests.post(url, headers=headers, json=data).json()
        if response.get("status") and response.get("data"):
            result = response.get("data").get("items")
            return {0: [
                {"id": item["id"], "name": item["name"], "is_leaf": True, "complete_name": [item["name"]]}
                for item in result]}
        else:
            return {0: []}

    async def list_collections(self, _page_limit: int = 50, collection_ids: list = None, site_type: str = "platform"):
        url = f"{self.isc_endpoint}/get_product_category_list"
        headers = self.get_auth_header()
        data = {
            "parent_id": 0
        }
        response = requests.post(url, headers=headers, json=data).json()
        if response.get("status") and response.get("data"):
            result = response.get("data").get("items")
            return {0: [
                {"id": item["id"], "name": item["name"], "is_leaf": True, "levels": [item["id"]]}
                for item in result]}
        else:
            return {0: []}

    async def get_variants(self, product_id, detail_level=None):
        def get_variant(item):
            result = {
                "name": item.get("name", "",),
                "price": item.get("list_price", 0),
                "image": item.get("main_image_url"),
                "stock": item.get("stock_quantity"),
                "sku": item.get("sku"),
                "id": item.get("id")
            }
            return result

        auth_header = self.get_auth_header()
        products_url = f"{self.isc_endpoint}/get_product_detail"
        if detail_level:
            params = {
              "product_id": product_id,
              "detail_level": detail_level,
            }
        else:
            params = {
                "product_id": product_id
            }
        response = requests.get(products_url, params=params, headers=auth_header).json()
        if response.get("data"):
            items = response.get("data").get("variants")
            return [get_variant(item) for item in items]
        else:
            return None

    async def get_products(self, products_dict):
        def make_order_list(order_dict):
            if not order_dict:
                return []
            _result = []
            for k, v in order_dict.items():
                _result.append(f"{k} {v}")
            return _result
        auth_header = self.get_auth_header()
        products_url = f"{self.isc_endpoint}/get_shop_product_list"
        data = {
            "page_size": products_dict.get("page_size"),
            "page": products_dict.get("begin_page"),
            "order": ["website_sequence asc"] + make_order_list(products_dict.get("order")),
            "keyword": products_dict.get("keyword"),
            "category": products_dict.get("category"),
            "language": products_dict.get("language"),
            "min_price": products_dict.get("price_start"),
            "max_price": products_dict.get("price_end"),
            "create_time_start": products_dict.get("create_time_start"),
            "create_time_end": products_dict.get("create_time_end"),
            "write_time_start": products_dict.get("write_time_start"),
            "write_time_end": products_dict.get("write_time_end"),
            "spu_list": [products_dict.get("spu")] if products_dict.get("spu") else None,
            "vendor_product_id_list": products_dict.get("vendor_product_ids")
        }
        log.info(f"product input is {data}")
        product_input = IscProductInput(**data).model_dump(exclude_none=True)
        try:

            response = requests.post(products_url, json=product_input, headers=auth_header).json()
            if response.get("data") and response.get("status"):
                res = response.get("data")
                result = {
                    "totalRecords": res.get("count"),
                    "totalPage": math.ceil(res.get("count") / products_dict.get("page_size")),
                    "pageSize": products_dict.get("page_size"),
                    "currentPage": products_dict.get("begin_page"),
                    "data": res.get("items"),
                }
                return result
            else:
                result = {
                    "totalRecords": 0,
                    "totalPage": 0,
                    "pageSize": products_dict.get("page_size"),
                    "currentPage": products_dict.get("begin_page"),
                    "data": []
                }
                return result
        except Exception as e:
            log.info(f"error is {str(e)}")

    def update_product_variant(self, variant_id, quantity, price):
        auth_header = self.get_auth_header()
        products_url = f"{self.isc_endpoint}/update_product_variant"
        data = {
            "variant_update_info_list": [
                {
                    "id": variant_id,
                    "new_quantity": quantity,
                    "new_price": price
                }
            ]
        }
        response = requests.post(products_url, json=data, headers=auth_header)
        if response.get("status"):
            return True
        else:
            return False

    async def get_product_variants(self, product_ids, source_connection_id=None, target_connection_id=None):
        def get_variant_name(attributes):
            variant_names = []
            if attributes:
                for attr in attributes:
                    name, value = attr
                    name = variant_names.append(f"{name}:{value}")
                return ", ".join(variant_names)
            return ""

        async def get_product_variant(product_id_input):
            product = await self.get_product(product_id_input)
            variants = []
            for variant in product.get("variants"):
                variant = {
                    "variant_id": variant.get("vendor_variant_id", ""),
                    "variant_image_url": variant.get("variant_image_url", ""),
                    "price": variant.get("standard_price"),
                    "stock": variant.get("stock_quantity"),
                    "name": get_variant_name(variant.get("attributes")),
                }
                variants.append(variant)
            return variants

        result = {}
        for product_id in product_ids:
            v = await get_product_variant(product_id)
            result[product_id] = v
        return result

    async def get_product(self, product_id):
        product = await self.get_isc_product(product_id)
        result = {
            "id": product.get("id"),
            "title": product.get("name"),
            "body_html": product.get("description") or product.get("website_description"),
            "tags": product["product_tags"] if product["product_tags"] else [],
            "main_image": product["main_image_url"] if product.get("main_image_url") else "",
            "images": product["img_urls"] if product.get("img_urls") else [],
            "status": "published",
        }
        return result

    async def product_update(self, product_info, product_id):
        """
        update data:
        "main_image_url": "https://cbu01.alicdn.com/img/ibank/O1CN01yC9B591Xl6Wi2C0wT_!!*************-0-cib.jpg",
        "sub_image_urls": [
            "https://cbu01.alicdn.com/img/ibank/O1CN01yC9B591Xl6Wi2C0wT_!!*************-0-cib.jpg"
        ],
        """
        auth_header = self.get_auth_header()
        products_url = f"{self.isc_endpoint}/update_product"
        result = {
            "product_id": product_id,
        }
        if product_info.get("main_image"):
            result["main_image_url"] = product_info.get("main_image")
        if product_info.get("images"):
            if len(product_info.get("images")) == 1:
                result["main_image_url"] = product_info.get("images")[0]
            else:
                result["sub_image_urls"] = product_info.get("images"),
        if product_info.get("name"):
            result['name'] = product_info.get("name")
        if product_info.get("description"):
            result['website_description'] = product_info.get("description")
        if product_info.get("tags"):
            result['product_tags'] = product_info.get("tags")

        json_data = {"product_update_info_list": [
            result
        ]}
        response = requests.post(products_url, json=json_data, headers=auth_header).json()
        result = response.get("data")
        return result

    async def product_search(self, page_info, page_limit, title, collection_id, page, refresh):
        auth_header = self.get_auth_header()
        products_url = f"{self.isc_endpoint}/get_shop_product_list"
        data = {
            "page_size": page_limit or 20,
            "page": page or 1,
            "keyword": title or "",
            "category": collection_id
        }
        log.info(f"product input is {data}")
        product_input = IscProductInput(**data).model_dump(exclude_none=True)
        response = requests.post(products_url, json=product_input, headers=auth_header).json()
        if response.get("data") and response.get("status"):
            result = []
            product_ids = [product["id"] for product in response.get("data").get("items")]
            for product_id in product_ids:
                product = await self.get_product(product_id)
                result.append(product)
            total_count = len(result)
            total_pages = math.ceil(total_count / page_limit)
            return {"products": result, "pagination": None, "total_pages": total_pages, "total_count": total_count}
        else:
            return {"products": [], "pagination": None, "total_pages": 1, "total_count": 0}

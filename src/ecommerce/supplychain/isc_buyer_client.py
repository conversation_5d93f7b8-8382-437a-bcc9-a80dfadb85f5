import math
import os

import requests
from elasticsearch import Elasticsearch, helpers
from redis import Redis

from db.pg_products_sync import get_full_sync_product_ids
from db.pg_site_info import get_site_info
from db.pg_site_provider_info import get_site_provider_info_by_id, get_auth_api_key
from db.pg_variants_sync import PGVariantsSync
from ecommerce.product.common_api import IscProduct, IscVariant
from ecommerce.supplychain.isc_base_client import IscBaseClient
from ecommerce.supplychain.models import IscProductInput
from ecommerce.supplychain.models import SaleOrderListModel
from redis_queue.redis_queue_service import get_waiting_product_ids, get_waiting_variant_ids
from utility.category_helper import find_category_es, get_root_category_es, make_search_item_es
from utility.config import ES_PORT, ES_HOST, REDIS_HOST, REDIS_PORT
from utility.elasticsearch_helpers import chunk_iterator, prepare_bulk_data
from utility.log import get_logger


def flatten_category(category):
    flattened = []

    def traverse(cat, parent_id=None, level=1):
        is_leaf = len(cat.get("child_categories", [])) == 0
        flattened.append(
            {
                "name": cat["name"],
                "category_id": cat["id"],
                "complete_name": cat["complete_name"],
                "id": cat["id"],
                "category_level": level,
                "is_leaf": is_leaf,
                "parent_id": parent_id,
            }
        )

        for child in cat.get("child_categories", []):
            traverse(child, cat["id"], level=level + 1)

    traverse(category)
    return flattened


log = get_logger(__name__)


def get_isc_url(site_id):
    result = get_site_provider_info_by_id(site_id)
    site_info = result.get("fields")
    if site_info and site_info.get("site_url"):
        return site_info.get("site_url")
    else:
        log.error(f"{site_id} site url not found")
        return None


def get_api_key(site_type):
    result = get_site_provider_info_by_id(site_type)
    site_info = result.get("fields")
    if site_info and site_info.get("nuwa_service_key"):
        return site_info.get("nuwa_service_key")
    else:
        log.error(f"{site_type} service key not found")
        return None


def get_isc_service_key(site_type):
    result = get_site_provider_info_by_id(site_type)
    site_info = result.get("fields")
    if site_info and site_info.get("isc_service_key"):
        return site_info.get("isc_service_key")
    else:
        log.error(f"{site_type} service key not found")
        return None


async def get_exclude_product_ids(source_site_connection_id, target_site_connection_id):
    waiting_product_ids = get_waiting_product_ids(source_site_connection_id, target_site_connection_id)
    full_sync_product_ids = await get_full_sync_product_ids(source_site_connection_id, target_site_connection_id)
    return list(set(waiting_product_ids + full_sync_product_ids))


class IscBuyerClient(IscBaseClient):
    def __init__(self, *, site_connection_id: int, **kwargs):
        # super().__init__(site_connection_id=site_connection_id, site_info_id=site_info_id, org_id=org_id)
        # self.token = token
        self.site_connection_id = site_connection_id
        site_info = get_site_info(site_connection_id)
        if site_info:
            self.site_id = site_info.get("site_id")
            self.site_type = site_info.get("site_type")
            self.site_info = site_info
        isc_url = get_isc_url(self.site_type)
        self.isc_endpoint = f"{isc_url}/isc"
        self.isc_service_endpoint = f"{isc_url}/isc_service"
        self.kwargs = kwargs

    def get_api_key(self):
        headers = {"X-API-Key": get_auth_api_key()}
        return headers

    def get_auth(self):

        redis_key = f"isc_client_token_{self.site_connection_id}"
        redis = Redis(host=REDIS_HOST, port=REDIS_PORT, db=2)
        auth_token = redis.get(redis_key)

        if auth_token:
            auth_token = auth_token.decode('utf-8')
        else:
            auth_url = os.environ.get("AUTH_URL") or "https://auth-stg.warpdriven.ai"
            isc_auth_url = f"{auth_url}/connection/isc_client/get_token"

            log.info(f'{isc_auth_url} start')

            auth_object = {
                "site_connection_id": self.site_connection_id,
            }

            response = requests.get(isc_auth_url, params=auth_object, headers=self.get_api_key())
            log.info(f'response {response.json()}')
            auth_token = response.json().get("data")
            log.info(f'{isc_auth_url} end, auth_token {auth_token}')
            redis.set(redis_key, auth_token, ex=3000)

        redis.close()

        return auth_token

    def get_auth_header(self):
        auth_token = self.get_auth()
        header = {"Authorization": f"Bearer {auth_token}"}
        return header

    def get_service_header(self):
        header = {"X-API-Key": get_isc_service_key(self.site_type)}
        return header

    def get_isc_service_header(self):
        header = {"X-API-Key": get_isc_service_key(self.site_type)}
        return header

    # def get_product_variants(self, product_id):
    #     auth_token = self.get_auth()
    #     header = self.get_auth_header(auth_token)
    #     return

    # def get_product_variant(self, variant_id):
    #     auth = self.get_auth()
    #     return

    async def get_product(self, product_id):
        auth_header = self.get_auth_header()
        products_url = f"{self.isc_endpoint}/get_product_detail"
        params = {
            "product_id": product_id,
        }
        response = requests.get(products_url, params=params, headers=auth_header).json()
        result = response.get("data")
        return result

    async def check_product(self, product_id):
        auth_header = self.get_auth_header()
        products_url = f"{self.isc_endpoint}/get_product_detail"
        params = {
            "product_id": product_id,
        }
        response = requests.get(products_url, params=params, headers=auth_header).json()
        if response.get("data"):
            return True
        else:
            return False

    # def
    # get_variant_count(self):
    #     auth = self.get_auth()
    #     return

    async def list_collections(self, _page_limit: int = 50, collection_ids: list = None, site_type="platform"):
        if collection_ids:
            collection = find_category_es("isc_collections", collection_ids)
        else:
            collection = get_root_category_es(index_name="isc_collections")
        return collection

    async def search_collections(self, search=None, site_type="platform"):
        if search:
            result = make_search_item_es(search, "isc_collections")
            return result
        else:
            return []

    async def list_languages(self):
        url = f"{self.isc_service_endpoint}/get_active_language"
        headers = self.get_service_header()
        response = requests.get(url, headers=headers).json()
        if response.get("status") and response.get("data"):
            result = response.get("data").get("items")
            return result
        else:
            return []

    async def get_currency_list(self):
        url = f"{self.isc_service_endpoint}/get_active_currency"
        headers = self.get_service_header()
        response = requests.get(url, headers=headers).json()
        if response.get("status") and response.get("data"):
            result = response.get("data").get("items")
            return result
        else:
            return []

    async def get_variants(self, product_id, detail_level=None, currency=None, target_connection_id=None):

        variant_ids = get_waiting_variant_ids(str(product_id), target_connection_id)
        if variant_ids:
            variant_ids = [int(i) for i in variant_ids]

        def get_variant_name(attributes):
            variant_names = []
            if attributes:
                for attr in attributes:
                    name, value = attr
                    name = variant_names.append(f"{name}:{value}")
                return ", ".join(variant_names)
            return ""

        def get_variant(item):
            variant_id = item.get("id")
            if variant_ids and variant_id in variant_ids:
                return None

            if not PGVariantsSync.check_variant(variant_id, target_connection_id):
                result = {
                    "name": get_variant_name(item.get("attributes_str")),
                    "price": item.get("list_price", 0),
                    "image": item.get("main_image_url"),
                    "stock": item.get("stock_quantity"),
                    "sku": item.get("sku"),
                    "id": variant_id,
                    "rrp": item.get("rrp"),
                }
                return result
            return None

        auth_header = self.get_auth_header()
        products_url = f"{self.isc_endpoint}/get_product_detail"
        log.info(f"products url is {products_url}")
        params = {
            "product_id": product_id,
        }
        if detail_level:
            params["detail_level"]: detail_level
        if currency:
            params["currency"] = currency
        log.info(f"get product detail params is {params}")
        response = requests.get(products_url, params=params, headers=auth_header).json()
        if response.get("data"):
            items = response.get("data").get("variants")
            log.info(f"items length is {len(items)}")
            return [get_variant(item) for item in items if get_variant(item)]
        else:
            return None

    def get_wishlist_products(self, products_dict):
        auth_header = self.get_auth_header()
        products_url = f"{self.isc_endpoint}/get_product_list"
        data = {
            "page_size": products_dict.get("page_size"),
            "page": products_dict.get("begin_page")
        }
        products_url = f"{self.isc_endpoint}/get_wishlist_product"
        response = requests.post(products_url, json=data, headers=auth_header).json()
        log.info(f"product response is {response}")
        if response.get("data") and response.get("status"):
            res = response.get("data")
            items = res.get("items")
            stock_items = []
            for item in items:
                stock = 0
                if item.get("variants"):
                    for variant in item.get("variants"):
                        stock += variant.get("stock_quantity", 0)
                item['stock'] = stock
                # item['stock'] = item['stock_quantity']
                # del item['stock_quantity']
                stock_items.append(item)
            result = {
                "totalRecords": res.get("count"),
                "totalPage": math.ceil(res.get("count") / products_dict.get("page_size")),
                "pageSize": products_dict.get("page_size"),
                "currentPage": products_dict.get("begin_page"),
                "data": stock_items
            }
            return result
        else:
            result = {
                "totalRecords": 0,
                "totalPage": 0,
                "pageSize": products_dict.get("page_size"),
                "currentPage": products_dict.get("begin_page"),
                "data": [],
            }
            return result

    async def get_products(self, products_dict, source_site_connection_id, target_site_connection_id=None):
        def make_order_list(order_dict):
            if not order_dict:
                return ['website_sequence asc']
            result = []
            for k, v in order_dict.items():
                if k == "spu":
                    result.append(f"default_code {v}")
                else:
                    result.append(f"{k} {v}")
            return result

        auth_header = self.get_auth_header()
        products_url = f"{self.isc_endpoint}/get_product_list"
        exclude_product_ids = await get_exclude_product_ids(source_site_connection_id, target_site_connection_id)
        data = {
            "page_size": products_dict.get("page_size"),
            "page": products_dict.get("begin_page"),
            "order": make_order_list(products_dict.get("order")),
            "keyword": products_dict.get("keyword"),
            "category_id": products_dict.get("category_id"),
            "language": products_dict.get("language"),
            "min_price": products_dict.get("price_start"),
            "max_price": products_dict.get("price_end"),
            "create_time_start": products_dict.get("create_time_start"),
            "create_time_end": products_dict.get("create_time_end"),
            "write_time_start": products_dict.get("write_time_start"),
            "write_time_end": products_dict.get("write_time_end"),
            "spu_list": [products_dict.get("spu")] if products_dict.get("spu") else None,
            "vendor_product_id_list": products_dict.get("vendor_product_ids"),
            "exclude_product_ids": [int(item) for item in list(set(exclude_product_ids))],
            "currency": products_dict.get("currency"),
            "custom_attribute": products_dict.get("custom_attribute")
        }
        log.info(f"api input is {data}")
        product_input = IscProductInput(**data).model_dump(exclude_none=True)
        log.info(f"product input is {product_input}")
        response = requests.post(products_url, json=product_input, headers=auth_header).json()
        # log.info(f"product response is {response}")
        if response.get("data") and response.get("status"):
            res = response.get("data")
            items = res.get("items")
            stock_items = []
            for item in items:
                item['stock'] = item['stock_quantity']
                del item['stock_quantity']
                stock_items.append(item)
            result = {
                "totalRecords": res.get("count"),
                "totalPage": math.ceil(res.get("count") / products_dict.get("page_size")),
                "pageSize": products_dict.get("page_size"),
                "currentPage": products_dict.get("begin_page"),
                "data": stock_items
            }
            return result
        else:
            result = {
                "totalRecords": 0,
                "totalPage": 0,
                "pageSize": products_dict.get("page_size"),
                "currentPage": products_dict.get("begin_page"),
                "data": [],
            }
            return result

    def update_product_variant(self, variant_id, quantity, price):
        auth_header = self.get_auth_header()
        products_url = f"{self.isc_endpoint}/update_product_variant"
        data = {"variant_update_info_list": [{"id": variant_id, "new_quantity": quantity, "new_price": price}]}
        response = requests.post(products_url, json=data, headers=auth_header)
        if response.get("status"):
            return True
        else:
            return False

    def get_system_product_attribute(self):
        auth_header = self.get_isc_service_header()
        url = f"{self.isc_service_endpoint}/get_system_product_attribute"
        response = requests.get(url, headers=auth_header)
        data = response.json()
        if data.get("data").get("items"):
            color_attributes = data.get("data").get("items")[0].get("values")
            return color_attributes
        else:
            return []

    async def get_product_variants(self, product_ids, target_connection_id=None):

        def get_variant_name(attributes: list):
            variant_names = []
            if attributes:
                for attr in attributes:
                    name, value = attr
                    variant_names.append(f"{name}:{value}")
                return ", ".join(variant_names)
            return ""

        async def get_product_variant(product_id):
            product = await self.get_product(product_id)
            variants = []
            for variant in product.get("variants"):
                exist_variant = PGVariantsSync.check_variant(variant.get("id"), target_connection_id)
                # log.info(f"target_connection_id is {target_connection_id}, exist variant is {exist_variant}")
                if not exist_variant:
                    variant = {
                        "variant_id": variant.get("id", ""),
                        "variant_image_url": variant.get("main_image_url", ""),
                        "price": variant.get("list_price"),
                        "stock": variant.get("stock_quantity"),
                        "name": get_variant_name(variant.get("attributes_str")),
                    }
                    variants.append(variant)
            return variants

        result = {}
        for product_id in product_ids:
            v = await get_product_variant(product_id)
            result[product_id] = v
        return result

    async def mapping_sync_product(self, product_id, variants_data, address):
        """
        variants_data is [{"variant_id":1}, {"variant_id":2}, ...]
        return isc_product, isc_variants
        """
        product = await self.get_product(product_id)
        variants = product.get("variants")
        isc_product = IscProduct(
            vendor_product_id=str(product_id),
            name=product.get("name"),
            spu=product.get("spu", ""),
            website_description=product.get("website_description", ""),
            category="",  # product.get("categories"),
            min_order_qty=1,
            wd_product_id=product.get("wd_product_id", ""),
            public_categories=[],  # product.get("categories"),
            sub_image_urls=product.get("img_urls"),
            main_image_url=product.get("main_image_url"),
            custom_attr_list=[],
            variants=[],
            variant_count=product.get("variant_count"),
            price_sale_list=[],  # [{"price": 100, "qty":10}]
            is_published=True,
            send_address=address,
            sale_ok=True,
            purchase_ok=True,
            barcode="",
            unit="",
            hs_code="",
            description=product.get("description", ""),
            description_sale=product.get("description_sale", ""),
            description_purchase=product.get("description_purchase", ""),
            website_meta_description=product.get("website_description", ""),
        ).model_dump()
        if variants is None:
            return isc_product, None
        result = []
        for variant in variants:
            variant_id = str(variant.get("id"))
            variants_data = [str(variant) for variant in variants_data]
            # log.info(f"variant_id is {variant_id}, variant_data is {variants_data}")
            if variant_id in variants_data:
                attributes = variant.get("attributes")
                attributes_str = []
                if attributes:
                    attributes_str = [
                        [attribute.get("attribute_type"), attribute.get("attribute_value")]
                        for attribute in attributes
                    ]
                variant_data = IscVariant(
                    vendor_variant_id=str(variant.get("id")),
                    wd_variant_id=variant.get("wd_variant_id"),
                    variant_image_url=variant.get("main_image_url"),
                    variant_images=variant.get("img_urls"),
                    stock_quantity=variant.get("stock_quantity"),
                    barcode=variant.get("barcode"),
                    standard_price=variant.get("list_price"),
                    rrp=variant.get("rrp"),
                    attributes=attributes_str,
                    dimensions={},
                    logistics={},
                    specific_id="",
                    sku=variant.get("sku", "")
                ).model_dump()
                result.append(variant_data)
        log.info(f"isc_product is {isc_product}, result is {result}")
        return isc_product, result

    def mapping_sync_variants(self, product_id, variants, address):
        if variants is None:
            return
        result = []
        for variant in variants:
            variant_id = variant.get("variant_id")
            variant = self.get_variant(variant_id)
            variant_data = IscVariant(
                vendor_variant_id=variant.get("vendor_variant_id"),
                wd_variant_id=variant.get("wd_variant_id"),
                variant_image_url=variant.get("variant_image_url"),
                stock_quantity=variant.get("stock_quantity"),
                barcode=variant.get("barcode"),
                standard_price=variant.get("standard_price"),
                attributes=variant.get("attributes"),
                dimensions={},
                logistics={},
                specific_id="",
            )
            result.append(variant_data)
        return result

    def make_wd_product_id(self, site_type, site_id, product_id):
        wd_product_id = f"WD_{site_type}_{site_id}_{product_id}"
        return wd_product_id

    def make_wd_variant_id(self, site_type, site_id, variant_id):
        wd_variant_id = f"WD_{site_type}_{site_id}_{variant_id}"
        return wd_variant_id

    # async def get_product_variants(self, product_ids):
    #     def get_variant_name(attributes):
    #         variant_names = []
    #         if attributes:
    #             for attr in attributes:
    #                 name, value = attr
    #                 name = variant_names.append(f"{name}:{value}")
    #             return ", ".join(variant_names)
    #         return ""
    #
    #     async def get_product_variant(product_id):
    #         product = await self.get_product(product_id)
    #         variants = []
    #         for variant in product.get("variants"):
    #             variant = {
    #                 "variant_id": variant.get("id", ""),
    #                 "variant_image_url": variant.get("main_image_url", ""),
    #                 "price": variant.get("list_price"),
    #                 "stock": variant.get("stock_quantity"),
    #                 "name": get_variant_name(variant.get("attributes_str")),
    #             }
    #             variants.append(variant)
    #         return variants
    #
    #     result = {}
    #     for product_id in product_ids:
    #         v = await get_product_variant(product_id)
    #         result[product_id] = v
    #     return result

    def get_variant_price_and_stock(self, source_product_id, source_variant_id):
        auth_header = self.get_auth_header()
        products_url = f"{self.isc_endpoint}/get_product_detail"
        params = {
            "variant_id": source_variant_id,
            "detail_level": "price_stock",
        }
        response = requests.get(products_url, params=params, headers=auth_header).json()
        if response.get("status"):
            result = response.get("data").get("items")
            return result[0]
        else:
            log.info(f"get variant {source_variant_id} failed")
            return None

    def get_product_price_and_stock(self, target_product_id):
        auth_header = self.get_auth_header()
        products_url = f"{self.isc_endpoint}/get_product_detail"
        params = {
            "product_id": target_product_id,
            "detail_level": "price_stock",
        }
        response = requests.get(products_url, params=params, headers=auth_header).json()
        if response.get("status"):
            result = response.get("data").get("items")
            return result
        else:
            log.info(f"get product {target_product_id} failed")
            return None

    def get_products_detail(self, product_ids: list[int], currency="AUD"):
        auth_header = self.get_isc_service_header()
        data = {
            "product_id_list": product_ids,
            "currency": currency
        }
        products_url = f"{self.isc_service_endpoint}/get_products_detail"
        response = requests.post(products_url, json=data, headers=auth_header).json()
        if response.get("status"):
            result = response.get("data").get("items")
            return result
        else:
            log.info(f"get product details failed, response is {response}")
            return None

    def update_variant_subscribed_status(self, update_items: list[dict]):
        """
        The example of update_items:
        [
        {
            "variant_id": 100,
            "is_subscribed": true
        },
        {
            "variant_id": 101,
            "is_subscribed": true
        },
        {
            "variant_id": 102,
            "is_subscribed": false
        }
        ]
        }
        """
        auth_header = self.get_isc_service_header()

        products_url = f"{self.isc_service_endpoint}/update_variant_subscribed_status"

        data = {
            "variant_subscribe_info_list": update_items,
        }

        log.info(f"data is {data}, headers is {auth_header}")
        response = requests.post(products_url, json=data, headers=auth_header).json()
        if response.get("status"):
            log.info(f"update_variant_subscribed_status: {update_items}")
            result = response.get("status")
            return result
        else:
            log.info(f"update status failed, response is {response}")
            return None

    def create_sale_orders(self, sale_orders: SaleOrderListModel, return_rsp=False):
        auth_header = self.get_auth_header()

        products_url = f"{self.isc_endpoint}/create_sale_orders"

        data = sale_orders.model_dump()
        log.info(f"sale order data is {data}")
        response = requests.post(products_url, json=data, headers=auth_header).json()
        if return_rsp:
            return response

        if response.get("status"):
            log.info(f"get sale orders")
            result = response.get("status")
            return result
        else:
            log.info(f"get sale order failed, response is {response}")
            return None

    def get_category(self):
        def get_name(name):
            for doc in category_list:
                if doc["name"] == name:
                    return doc["id"]

        url = f"{self.isc_endpoint}/get_hierarchy_category"
        headers = self.get_auth_header()
        response = requests.get(url, headers=headers).json()
        if response.get("status") and response.get("data"):
            result = response.get("data")
            log.info(f"result is {result}")
            category_list = []
            for item in result:
                category_list.extend(flatten_category(item))
            result = []
            for category in category_list:
                levels = []
                names = category["complete_name"].split(" / ")
                for name in names:
                    category_id = get_name(name)
                    levels.append(category_id)
                category["levels"] = levels
                result.append(category)
            return result
        else:
            return []

    def export_category_to_es(self, chunk_size=1000):
        try:
            # Connect to Elasticsearch
            es = Elasticsearch(
                hosts=f"http://{ES_HOST}:{ES_PORT}",
                timeout=60
            )
            index_name = "isc_collections"

            # Read JSON Data
            data = self.get_category()
            total_docs = len(data)

            # Process in chunks with progress bar
            success_count = 0
            error_count = 0

            log.info(f"Starting bulk import of {total_docs} documents")
            for chunk in chunk_iterator(data, chunk_size):
                try:
                    # Perform bulk operation
                    success, errors = helpers.bulk(
                        es,
                        prepare_bulk_data(chunk, index_name),
                        raise_on_error=False
                    )

                    success_count += success
                    error_count += len(errors) if errors else 0

                except Exception as e:
                    log.info(f"Error during bulk operation: {str(e)}")
                    error_count += len(chunk)

            # Log results
            log.info(f"""
            Import completed:
            - Total documents: {total_docs}
            - Successfully imported: {success_count}
            - Failed: {error_count}
            """)
            return True

        except Exception as e:
            log.info(f"Fatal error: {str(e)}")
            return False

    async def get_variant_logistics_info(self, product_id, variant_id, currency: str | None = None):
        auth_header = self.get_auth_header()
        products_url = f"{self.isc_endpoint}/get_variants_info"
        data = {
            "variant_id_list": [variant_id],
            "currency": currency if currency else "USD",
        }
        response = requests.post(products_url, json=data, headers=auth_header).json()
        if response.get("status"):
            result = response.get("data").get("items")
            return result[0] if len(result) > 0 else None
        else:
            log.info(f"get variant {variant_id} failed")
            return None

    async def get_variants_info(
            self, product_id: str | None = None,
            variant_ids: list = None,
            origin_currency: str = 'USD',
            currency: str = 'USD'
    ):

        auth_header = self.get_auth_header()
        products_url = f"{self.isc_endpoint}/get_variants_info"
        log.info(f'{products_url} start {product_id} {variant_ids} {origin_currency} {currency}')
        data = {
            "variant_id_list": variant_ids,
            "currency": currency,
            "origin_currency": origin_currency
        }
        response = requests.post(products_url, json=data, headers=auth_header).json()
        log.info(f'{products_url} end {response}')
        if response.get("status"):
            result = response.get("data").get("items")
            return result if len(result) > 0 else None
        else:
            log.info(f"get variants {variant_ids} failed")
            return None

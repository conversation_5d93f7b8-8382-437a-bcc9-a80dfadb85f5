import json
from decimal import Decimal

from fastapi.encoders import jsonable_encoder

from db.pg_async_metadata import get_metadata_erp_url
from db.pg_site_provider_info import get_wd_service_api_key
from ecommerce.warpdrive.warpdriven_erp_model import ShipmentOrderSchema
from utility.log import get_logger

ERP_URL = f"{get_metadata_erp_url()}"
from utility.async_http_request import aiohttp_call

log = get_logger(__name__)


async def get_api_key_headers():
    api_key = await get_wd_service_api_key('WarpDrivenAI')
    return {'content-type': 'application/json;charset=UTF-8', 'X-API-Key': api_key}


async def get_token_headers(token: str):
    return {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json;charset=UTF-8'
    }


async def _request_post_api_key(url='/', payload=None, timeout: int = 1200):
    full_url = f'{ERP_URL}/service/{url}'
    log.info(f"_request_post_api_key {full_url} start")
    headers = await get_api_key_headers()
    payload = jsonable_encoder(payload)
    result = await aiohttp_call(
        rpc_url=full_url, payload=json.dumps(payload), method='POST', timeout=timeout, headers=headers
    )
    log.info(f"_request_post_api_key {full_url} end, result = {result}")
    return result


async def _request_get_api_key(url='/', payload=None, timeout: int = 1200):
    full_url = f'{ERP_URL}/service/{url}'
    log.info(f"_request_get_api_key {full_url} start")
    headers = await get_api_key_headers()
    result = await aiohttp_call(
        rpc_url=full_url, payload=payload, method='GET', timeout=timeout, headers=headers
    )
    log.info(f"_request_get_api_key {full_url} end, result = {result}")
    return result


async def _request_post_token(url='/', payload=None, timeout: int = 1200, token: str = None):
    full_url = f'{ERP_URL}/wd/{url}'
    log.info(f"_request_post_token {full_url} start")
    headers = await get_token_headers(token)
    payload = jsonable_encoder(payload)
    result = await aiohttp_call(
        rpc_url=full_url, payload=json.dumps(payload), method='POST', timeout=timeout, headers=headers
    )
    log.info(f"_request_post_token {full_url} end, result = {result}")
    return result


async def _request_get_token(url='/', payload=None, timeout: int = 1200, token: str = None):
    full_url = f'{ERP_URL}/wd/{url}'
    log.info(f"_request_get_token {full_url} start")
    headers = await get_token_headers(token)
    result = await aiohttp_call(
        rpc_url=full_url, payload=payload, method='GET', timeout=timeout, headers=headers
    )
    log.info(f"_request_get_token {full_url} end, result = {result}")
    return result


async def get_account_config(platform_code: int, token: str):
    payload = {
        'platform_code': platform_code
    }
    result = await _request_get_token(url=f'get_account_config?platform_code={platform_code}', payload=payload,
                                      token=token)
    return result['data'] if result['status'] else None


async def get_account_discount_by_service_key(
        platform_code: int, user_id: int, amount: Decimal = None,
        currency_code: str = None
):
    payload = {
        'user_id': user_id,
        'platform_code': platform_code,
        'amount': amount,
        'currency_code': currency_code
    }
    result = await _request_get_api_key(url='get_account_discount', payload=payload)
    return result['data'] if result['status'] else None


async def get_account_config_by_service_key(
        platform_code: int, user_id: int
):
    payload = {
        'user_id': user_id,
        'platform_code': platform_code
    }
    result = await _request_get_api_key(url='get_account_config', payload=payload)
    return result['data'] if result['status'] else None


async def get_order_checkout_info(platform_code: str, logistics_info: dict, token: str):
    payload = {
        'platform_code': platform_code,
        'logistics_info': logistics_info
    }
    result = await _request_post_token(url='get_order_checkout_info', payload=payload, token=token)
    return result['data'] if result['status'] else None


async def get_logistics_routes(
        provider_type: str, weight: Decimal, receiver_country_code: str, sender_country_code: str = "CN",
        include_battery=False
):
    payload = {
        {
            "provider_type": provider_type,
            "include_battery": False,
            "sender_country_code": sender_country_code,
            "receiver_country_code": receiver_country_code,
            "weight": weight
        }
    }
    result = await _request_post_api_key(url='get_logistics_routes', payload=payload)
    return result.get("data", {}).get("matched_routes", [])


async def get_estimated_cost(payload: dict, token):
    result = await _request_get_token(url='get_estimated_cost', payload=payload, token=token)
    return result['data'] if result['status'] else result['msg']


async def calc_logistics_route_cost(
        provider_type: str,
        route_code: str,
        parcel_info: dict,
        recipient_info: dict,
        sender_info: dict,
        currency_code: str, token
):
    payload = {
        "provider_type": provider_type,
        "parcel_info": parcel_info,
        "recipient_info": recipient_info,
        "sender_info": sender_info,
        "currency_code": currency_code,
        "route_code": route_code
    }
    result = await _request_post_token(url='calc_logistics_route_cost', payload=payload, token=token)
    return result['data'] if result['status'] else result['msg']


async def get_invoice_address(token: str):
    result = await _request_post_token(url='user/get_invoice_address', payload={}, token=token)
    return result['data'] if result['status'] else None


async def get_delivery_address(token: str):
    result = await _request_get_token(url='query_merchant_address', payload={}, token=token)
    address_list = result.get("data", {}).get("address_list", [])
    if address_list and len(address_list) > 0:
        return address_list[0]
    else:
        return None


async def get_invoice_address_by_service_key(user_id: int):
    payload = {
        'user_id': user_id
    }
    result = await _request_get_api_key(url='user/get_invoice_address', payload=payload)
    return result['data'] if result['status'] else None


async def get_oauth_user_info_by_service_key(user_id: int, site_provider_id: int):
    payload = {
        'user_id': user_id,
        'site_provider_id': site_provider_id
    }
    result = await _request_get_api_key(url='get_oauth_user_info', payload=payload)
    return result['data']["oauth_uid"] if result['status'] else None


async def calc_logistics_route_cost_post_by_service_key(
        provider_type: str,
        route_code: str,
        parcel_info: dict,
        recipient_info: dict,
        sender_info: dict,
        currency_code: str
):
    payload = {
        "provider_type": provider_type,
        "parcel_info": parcel_info,
        "recipient_info": recipient_info,
        "sender_info": sender_info,
        "currency_code": currency_code,
        "route_code": route_code
    }
    result = await _request_post_api_key(url='calc_logistics_route_cost', payload=payload)
    return result['data'] if result.get('status', False) else None


async def get_currency_info():
    result = await _request_post_api_key(url='user/get_currency_info', payload={})
    return result['data'] if result['status'] else None


async def cancel_purchase_order_payment(purchase_order_id: int, token: str):
    result = await _request_get_token(url='cancel_purchase_order_payment', payload={
        'purchase_order_id': purchase_order_id
    }, token=token)
    return result['status']


async def create_shipment_order(shipment_order: ShipmentOrderSchema):
    result = await _request_post_api_key(url='create_shipment_order', payload=shipment_order.model_dump())
    assert result.get('status', False), f'{get_error_massage(result)}'
    return result['data']


def get_error_massage(result: dict):
    return result.get("detail")

import asyncio
import json

from db.pg_auto_sync_settings import PGAutoSyncSettings
from db.pg_products_sync import PGProductsSync
from db.pg_site_connection import get_site_connection_ids_by_site_type
from db.pg_site_connection import get_website_info, get_website_role_type
from db.pg_site_provider_info import get_site_provider_info_by_id
from db.pg_variants_sync import PGVariantsSync
from ecommerce.base_api import SyncOption, PriceInfo, PriceMethod
from ecommerce.base_api_models import ProductSyncInput, ProductSync
from ecommerce.product.common_api import get_exchange_rate
from ecommerce.product_sync import product_sync_service
from ecommerce.product_sync.adapters.IscSupplyAdapter import IscSupplyAdapter
from ecommerce.product_sync.adapters.ShopifyStoreAdapter import ShopifyStoreAdapter
from ecommerce.product_sync.adapters.WooStoreAdapter import WooStoreAdapter
from ecommerce.product_sync.nuwa_sync import ProductSyncHelper, PriceStockSettings
from ecommerce.product_sync.product_sync_model import SupplyProductSchema, StoreProductSchema, SupplyVariantSchema
from redis_queue.redis_queue_enum import JobType
from redis_queue.redis_queue_handler import RedisQueueHelper
from redis_queue.redis_queue_service import get_waiting_variant_ids
from utility.api_helper import CommonHeaders
from utility.log import get_logger

log = get_logger(__name__)


class ProductSyncOrchestrator:
    _supply_adapter = {
        "ISCPlatform_Italy": IscSupplyAdapter,
        "ISCPlatform_emutree": IscSupplyAdapter
    }

    _store_adapter = {
        "Woocommerce": WooStoreAdapter,
        "Shopify": ShopifyStoreAdapter
    }

    def __init__(
            self, org_id: int = None, token: str = None, common_headers: CommonHeaders | None = None
    ):
        self.supply_adapter = None
        self.store_adapter = None
        self.price_info = None
        self.sync_option = None
        self.collection_id = None
        self.ebay_policy_id = None
        self.exchange_rate = None
        self.source_site_connection_id = None
        self.target_site_connection_id = None
        self.org_id = org_id
        self.token = token
        self.common_headers = common_headers

    @staticmethod
    def get_site_provider_info(source_site_connection_id: int):
        website_info = get_website_info(source_site_connection_id)
        assert website_info, "Failed to get website info"
        site_type = website_info.site_type
        site_provider_info = get_site_provider_info_by_id(site_type)
        assert site_provider_info, "Failed to get site provider info"
        role_type = get_website_role_type(source_site_connection_id)
        return website_info, site_provider_info, role_type

    def init_supply_adapter(self, source_site_connection_id: int, org_id: int, token: str):
        self.source_site_connection_id = source_site_connection_id
        website_info, site_provider_info, role_type = ProductSyncOrchestrator.get_site_provider_info(
            source_site_connection_id
        )
        issuer = site_provider_info.get("issuer")
        supply_adapter_class = ProductSyncOrchestrator._supply_adapter.get(issuer)
        assert supply_adapter_class, f"Adapter not found for {issuer}"
        self.supply_adapter = supply_adapter_class(
            source_site_connection_id, org_id, token,
            locale=self.common_headers.locale,
            language=self.common_headers.x_user_language,
            currency=self.common_headers.x_user_currency
        )
        self.supply_adapter.init(website_info, site_provider_info, role_type)

    def create_store_adapter(self, target_site_connection_id: int, org_id: int, token: str):
        self.target_site_connection_id = target_site_connection_id
        website_info, site_provider_info, role_type = ProductSyncOrchestrator.get_site_provider_info(
            target_site_connection_id
        )
        issuer = site_provider_info.get("issuer")
        store_adapter_class = ProductSyncOrchestrator._store_adapter.get(issuer)
        assert store_adapter_class, f"Adapter not found for {issuer}"
        store_adapter = store_adapter_class(
            target_site_connection_id, org_id, token,
            locale=self.common_headers.locale,
            language=self.common_headers.x_user_language,
            currency=self.common_headers.x_user_currency
        )
        store_adapter.init(website_info, site_provider_info, role_type)
        return store_adapter

    def init_store_adapter(self, target_site_connection_id: int, org_id: int, token: str):
        self.store_adapter = self.create_store_adapter(target_site_connection_id, org_id, token)

    def init_sync_params(
            self, price_info: PriceInfo, sync_option: SyncOption, collection_id, ebay_policy_id, exchange_rate
    ):
        self.price_info = price_info
        self.sync_option = sync_option
        self.collection_id = collection_id
        self.ebay_policy_id = ebay_policy_id
        self.exchange_rate = exchange_rate

    async def sync_products(
            self, source_site_connection_id: int, target_site_connection_id: int, products_info: ProductSyncInput,
            price_info: PriceInfo, collection_id, ebay_policy_id,
            sync_option: SyncOption, user_id
    ):
        self.init_supply_adapter(source_site_connection_id, self.org_id, self.token)
        self.init_store_adapter(target_site_connection_id, self.org_id, self.token)
        exchange_rate = await self.get_exchange_rate(price_info.target_currency)
        self.init_sync_params(price_info, sync_option, collection_id, ebay_policy_id, exchange_rate)

        await self.save_price_stock_settings(products_info.is_publish, price_info, sync_option)

        redis_queue_helper = RedisQueueHelper(user_id=user_id, job_type=JobType.PRODUCT_SYNC)
        for product in products_info.products:
            variant_ids = []
            is_fully_synced = True
            if product.variants:
                variant_ids = [str(variant.variant_id) for variant in product.variants]
                is_fully_synced = False

            meta = {
                "source_site_connection_id": self.source_site_connection_id,
                "target_site_connection_id": self.target_site_connection_id,
                "user_id": user_id,
                "is_fully_synced": is_fully_synced,
                "variant_ids": variant_ids,
                "product_id": product.product_id,
                "product_name": product.product_name,
                "product_sku": product.product_sku,
                "product_image": product.product_image,
                "duration_ms": 0
            }

            redis_queue_helper.add_job(
                asyncio_product_sync,
                self, product,
                ref_id=str(product.product_id),
                job_timeout=30 * 60,
                result_ttl=60 * 60,
                ttl=24 * 60 * 60,
                failure_ttl=24 * 60 * 60,
                meta=meta
            )

    async def test_sync_products(
            self, source_site_connection_id: int, target_site_connection_id: int, products_info: ProductSyncInput,
            price_info: PriceInfo, collection_id, ebay_policy_id,
            sync_option: SyncOption, user_id
    ):
        self.init_supply_adapter(source_site_connection_id, self.org_id, self.token)
        self.init_store_adapter(target_site_connection_id, self.org_id, self.token)
        exchange_rate = await self.get_exchange_rate(price_info.target_currency)
        self.init_sync_params(price_info, sync_option, collection_id, ebay_policy_id, exchange_rate)
        await self.save_price_stock_settings(products_info.is_publish, price_info, sync_option)
        for product in products_info.products:
            if product.variants:
                variant_ids = [str(variant.variant_id) for variant in product.variants]
                is_fully_synced = False
            await self.sync_product_variants(product)

    async def sync_product_variants(self, product: ProductSync):
        product_id = product.product_id
        try:
            supply_product = await self.supply_adapter.fetch_product_and_variants(product.product_id)
            (supply_product, supply_variants) = await self.make_sync_product(supply_product)
            log.info(f"supply product data is {json.dumps(supply_product.model_dump())}")
            assert supply_product, f"Failed to fetch product {product_id}"
            store_product = await self.store_adapter.push_product_and_variants(supply_product)
            log.info(f"shop_product: {store_product}")
            await self.sync_product_success(supply_product, store_product, supply_variants)
        except Exception as e:
            log.exception(e)
            raise e

    async def make_sync_product(
            self, supply_product: SupplyProductSchema
    ) -> (SupplyProductSchema, list[SupplyVariantSchema]):

        if supply_product:

            supply_product.collection_id = self.collection_id

            target_product_id = PGProductsSync.get_target_product_id(
                supply_product.product_id,
                self.store_adapter.website_info.id
            )
            supply_product.target_product_id = target_product_id

            synced_variants = PGVariantsSync.get_synced_variants(
                supply_product.product_id,
                self.store_adapter.website_info.id
            )

            synced_variants_id_mapping = {
                variant.source_variant_id: variant.target_variant_id for variant in
                synced_variants
            }

            filter_variant_ids = get_waiting_variant_ids(
                str(supply_product.product_id), self.target_site_connection_id
            )

            supply_variants = [
                variant for variant in supply_product.variants
            ]

            if filter_variant_ids:
                supply_product.variants = [
                    variant for variant in supply_product.variants if variant.variant_id in filter_variant_ids
                ]

            for variant in supply_product.variants:
                variant.standard_price = await self.price_func(variant.standard_price)
                variant.rrp = await self.price_func(variant.rrp)
                variant.target_variant_id = synced_variants_id_mapping.get(variant.variant_id)

            return supply_product, supply_variants
        return None

    async def sync_product_success(
            self, supply_product: SupplyProductSchema, store_product: StoreProductSchema,
            supply_variants: list[SupplyVariantSchema]
    ):
        product_sync_instance = ProductSyncHelper(org_id=self.org_id, site_info=self.store_adapter.website_info)
        if store_product:
            _product_sync_item = {
                "source_site_connection_id": self.source_site_connection_id,
                "target_site_connection_id": self.target_site_connection_id,
                "site_info_id": self.store_adapter.website_info.id,
                "source_product_id": supply_product.product_id,
                "target_product_id": store_product.target_product_id,
                "wd_product_id": supply_product.wd_product_id,
                "product_name": supply_product.name,
                "product_sku": supply_product.spu
            }

            products_sync_id = product_sync_instance.sync(sync_item=_product_sync_item, level="product")

            for index, supply_variant in enumerate(supply_product.variants):
                shop_variant = store_product.variants[index]
                _variant_sync_item = {
                    "source_site_connection_id": self.source_site_connection_id,
                    "target_site_connection_id": self.target_site_connection_id,
                    "products_sync_id": products_sync_id,
                    "source_product_id": supply_product.product_id,
                    "source_variant_id": supply_variant.variant_id,
                    "wd_variant_id": supply_variant.wd_variant_id,
                    "wd_product_id": supply_product.wd_product_id,
                    "target_product_id": store_product.target_product_id,
                    "target_variant_id": shop_variant.target_variant_id,
                    "variant_sku": supply_variant.sku,
                    "price": supply_variant.rrp,
                    "stock_quantity": supply_variant.stock_quantity
                }
                product_sync_instance.sync(sync_item=_variant_sync_item, level="variant")

            supply_variant_ids = list(set([str(variant.variant_id) for variant in supply_variants]))

            log.info(f"supply variant ids: {supply_variant_ids}")

            synced_variants = PGVariantsSync.get_synced_variants(
                supply_product.product_id,
                self.store_adapter.website_info.id
            )

            synced_variant_ids = list(set([str(variant.source_variant_id) for variant in synced_variants]))
            synced_variant_ids = [vid for vid in synced_variant_ids if vid in supply_variant_ids]

            log.info(f"synced variant ids: {synced_variant_ids}")

            all_variants_synced = (len(supply_variant_ids) == len(synced_variant_ids)) and all(
                variant_id in supply_variant_ids for variant_id in synced_variant_ids
            )

            log.info(f"all variants synced: {all_variants_synced}")
            product_sync_instance.update_synced_product(
                update_detail={"is_fully_synced": all_variants_synced},
                **_product_sync_item,
            )

            await self.update_variant_subscribed_status(self.store_adapter.website_info.id, supply_variant_ids)

    async def save_price_stock_settings(self, is_published, price_info: PriceInfo, sync_option: SyncOption):
        settings_data = {
            "org_id": self.org_id,
            "site_info_id": self.store_adapter.website_info.id,
            "price_status": sync_option.price,
            "stock_status": sync_option.stock,
            "is_published:": is_published,
            "price_value": price_info.price_value,
            "price_method": price_info.price_method,
            "target_currency": price_info.target_currency,
        }
        price_stock_instance = PriceStockSettings(**settings_data)
        price_stock_instance.sync()

    async def get_exchange_rate(self, target_currency: str):
        currency_list = await self.supply_adapter.get_currency_list()
        assert currency_list, "Failed to get currency list"
        base_currency = currency_list[0].get("code") if currency_list else 'CNY'
        if base_currency == target_currency:
            return 1
        return await get_exchange_rate(target_currency, base_currency)

    async def price_func(self, price):
        price_method = self.price_info.price_method
        price_value = self.price_info.price_value
        if price_method == PriceMethod.PERCENTAGE:
            return round(price * (1 + price_value / 100) * self.exchange_rate, 2)
        if price_method == PriceMethod.FIXED_VALUE:
            fix_amount = price_value
            return round(price * self.exchange_rate, 2) + fix_amount
        if price_method == PriceMethod.RECOMMENDED_RETAIL_PRICE:
            return round(price * self.exchange_rate, 2)
        return price

    async def update_variant_subscribed_status(self, site_info_id: int, supply_variant_ids: list[str]):
        sync_setting = PGAutoSyncSettings.get_auto_sync_settings(site_info_id)
        if sync_setting:
            sync_type = sync_setting.get("sync_type")
            if sync_type == 2:
                json_data = [{
                    "variant_id": variant_id,
                    "is_subscribed": True
                } for variant_id in supply_variant_ids]
                result = await self.supply_adapter.update_variant_subscribed_status(json_data)
                if result:
                    log.info(f"sync product subscribe status successful, subscribe items are {json_data}")
                else:
                    log.info(f"sync product subscribe status failed")

    async def update_variant_stock_quantity(
            self, site_provider_id, items
    ):
        if items:
            source_site_connection_ids = get_site_connection_ids_by_site_type(site_provider_id)
            if source_site_connection_ids and len(source_site_connection_ids) > 0:
                source_product_ids = list(set([str(item.product_id) for item in items]))
                source_variant_ids = list(set([str(item.variant_id) for item in items]))
                await product_sync_service.update_variant_price_and_stock(
                    source_site_connection_ids, items
                )
                target_variants = await product_sync_service.get_target_site_connection_variants_stock_quantity(
                    source_site_connection_ids, source_product_ids, source_variant_ids
                )
                target_store_adapter_map = {}
                target_store_variants_map = {}
                if target_variants:
                    for target_variant in target_variants:
                        target_store_adapter = target_store_adapter_map.get(target_variant.target_site_connection_id)
                        target_store_variants = target_store_variants_map.get(target_variant.target_site_connection_id)
                        if not target_store_adapter:
                            target_store_adapter = self.create_store_adapter(
                                target_variant.target_site_connection_id, self.org_id, self.token
                            )
                            target_store_adapter_map[target_variant.target_site_connection_id] = target_store_adapter
                        if not target_store_variants:
                            target_store_variants = []
                            target_store_variants_map[target_variant.target_site_connection_id] = target_store_variants

                        target_store_variants.append(target_variant)
                    tasks = []
                    for target_site_connection_id, target_store_adapter in target_store_adapter_map.items():
                        target_store_variants = target_store_variants_map.get(target_site_connection_id)
                        tasks.append(
                            target_store_adapter.update_variant_price_and_stock(target_store_variants)
                        )
                    try:
                        await asyncio.gather(*tasks)
                    except Exception as e:
                        log.warning(e)


def asyncio_product_sync(orchestrator, product: ProductSync):
    asyncio.run(orchestrator.sync_product_variants(product))

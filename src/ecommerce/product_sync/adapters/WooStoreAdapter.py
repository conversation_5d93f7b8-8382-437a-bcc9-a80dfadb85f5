import asyncio

from ecommerce import factory
from ecommerce.base_api_models import ItemInput
from ecommerce.product_sync.adapters.BaseStoreAdapter import BaseStoreAdapter
from ecommerce.product_sync.product_sync_model import SupplyProductSchema, StoreProductSchema
from utility.log import get_logger

log = get_logger(__name__)


class WooStoreAdapter(BaseStoreAdapter):

    def __init__(self, site_connection_id, org_id=None, token=None,
                 locale="en-AU;q=0.9,en;q=0.8", language="en", currency="AUD"):
        super().__init__(site_connection_id, org_id, token, locale, language, currency)
        self.woo_client = factory.get_eshop(
            site_connection_id=site_connection_id, token=token, locale=locale,
            language=language, currency=currency
        )

    def push_product_and_variants(self, supply_product: SupplyProductSchema) -> StoreProductSchema:
        pass

    async def update_variant_price_and_stock(
            self, items: list[ItemInput]
    ):
        tasks = []
        for item in (items or []):
            log.info(f"update variant {item.target_variant_id} stock, items are {items}")
            tasks.append(
                asyncio.to_thread(
                    self.woo_client.update_variant_price_and_stock,
                    item.target_product_id,
                    item.target_variant_id,
                    stock_quantity=item.stock_quantity
                )
            )
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            log.warning(e)

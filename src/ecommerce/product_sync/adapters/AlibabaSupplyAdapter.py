import math
from os import environ

import redis
from babel.numbers import format_currency
from fastapi.encoders import jsonable_encoder

from ecommerce import factory
from ecommerce.product.common_api import get_exchange_rate, get_vendor_code
from ecommerce.product_sync.adapters.IscSupplyAdapter import IscSupplyAdapter
from supplier.affiliates.alibaba.ali_data_service import *

from supplier.affiliates.alibaba.isc_data_load import is_zh, remove_substrings
from utility import currency_utils
from utility.config import REDIS_HOST, REDIS_PORT
from utility.currency_utils import convert_target_currency
from utility.exception_handler import BusinessException
from utility.log import get_logger

log = get_logger(__name__)
redis_async_client = redis.asyncio.Redis(host=REDIS_HOST, port=REDIS_PORT, db=2)

CACHE_EXPIRATION_TOP_LIST = 3600 * 2  # cache get product 60*60 seconds
CACHE_EXPIRATION_TOP_LIST_RAW = 3600 * 2  # cache get product 60*60 seconds
enable_top_list_cache = environ.get("ENABLE_TOP_LIST_CACHE", "true").lower() == "true"
enable_top_list_raw_cache = environ.get("ENABLE_TOP_LIST_RAW_CACHE", "true").lower() == "true"


def get_top_list_cache_key(
        category_id: int | None = None, language='en', currency='AUD',
        begin_page: int = 1, page_size: int = 30, rank_type: str = "complex"
):
    if not category_id:
        category_id = "all"
    cache_key = (f"cache:1688_top_products:"
                 f"{category_id}_{language.lower()}_{currency.lower()}_{rank_type}_{begin_page}_{page_size}"
                 )

    cache_total_count_key = f'cache:1688_top_products:total_count_{category_id}'
    return cache_key, cache_total_count_key


def get_top_list_raw_cache_key(
        category_id: int | None = None,
        rank_type: str = "complex"
):
    if not category_id:
        category_id = "all"
    cache_key = f"cache:1688_top_raw_products:{category_id}_{rank_type}"

    return cache_key


class AlibabaSupplyAdapter(IscSupplyAdapter):
    def __init__(self, site_connection_id, org_id=None, token=None,
                 locale="en-AU;q=0.9,en;q=0.8", language="en", currency="AUD"):
        super().__init__(site_connection_id, org_id, token, locale, language, currency)
        self.site_type = 201
        self.buyer_id = '6821357'
        self.vendor_code = get_vendor_code(site_type=self.site_type, buyer_id=self.buyer_id)

        self.alibaba_client = factory.get_eshop(
            site_connection_id=site_connection_id, token=token, locale=locale, language=language, currency=currency
        )

    async def get_exchange_rate(self, target_currency: str):
        base_currency = await self.get_base_currency()
        return await get_exchange_rate(target_currency, base_currency)

    async def get_base_currency(self):
        return "CNY"

    def get_variant_name(self, variant) -> list:
        attributes_all = []
        attributes = variant.get("skuAttributes")
        if attributes:
            for att in attributes:
                variant_des = dict()
                variant_des['name'] = att.get("attributeName" if is_zh(self.language) else "attributeNameTrans")
                variant_des['value'] = att.get("value" if is_zh(self.language) else "valueTrans")
                attributes_all.append(variant_des)
        return attributes_all

    async def search_products(
            self,
            offer_query_param,
            min_month_sold=1,
            begin_page: int = 1,
            page_size: int = 30,
            rank_type: str = "complex"
    ):
        keyword = offer_query_param.get("keyword")

        if not keyword:
            category_id = offer_query_param.get("categoryId")

            cache_top_list_key, cache_total_count_key = get_top_list_cache_key(
                category_id, self.language, self.currency,
                begin_page, page_size, rank_type
            )
            cached_product = await redis_async_client.get(cache_top_list_key)
            cached_count = await redis_async_client.get(cache_total_count_key)

            if cached_product and enable_top_list_cache:
                ranked_products = json.loads(cached_product)
                total_count = (int(cached_count.decode('utf-8')) if cached_count else None) or len(ranked_products)
            else:
                try:
                    ranked_products, total_count = await self.search_top_list_products(
                        category_id=category_id,
                        begin_page=begin_page, page_size=page_size, rank_type=rank_type
                    )
                    await redis_async_client.setex(
                        cache_total_count_key,  # key 名称，例如: "product:total_count"
                        CACHE_EXPIRATION_TOP_LIST,  # 过期时间（秒）
                        str(total_count)  # 将整数转换为字符串存储
                    )
                    await redis_async_client.setex(
                        cache_top_list_key,
                        CACHE_EXPIRATION_TOP_LIST,
                        json.dumps(ranked_products)
                    )
                except BusinessException as e:
                    log.warning(f"Failed to get top list products: {e}")
                    ranked_products = []
                    total_count = 0

            return {
                "totalRecords": total_count,
                "totalPage": math.ceil(total_count / page_size),
                "pageSize": page_size,
                "currentPage": begin_page,
                "category_id": category_id,
                "rank_type": rank_type,
                "products": ranked_products
            }

        if keyword and keyword.isdigit():
            # if the keyword is a number, we assume it is a offer id,
            # but if the query result is empty,we will try to search by keyword
            product = await get_ali_product_details(int(keyword), self.site_connection_id)

            pd_info = {
                "totalRecords": 1,
                "totalPage": 1,
                "pageSize": 1,
                "currentPage": 1,
                "open_detail_page": True,
                "products": [
                    await self.transform_product_detail_list_info(
                        product
                    )
                ] if product else []
            }
            return pd_info

        else:
            api_name = "product.search.keywordQuery"

            result = await call_ali_api(
                offer_query_param, api_name, self.site_connection_id
            )

            if result:
                products = result.pop('data') if ('data' in result) else []
                result["products"] = (
                    await self.batch_transform_keyword_query_to_product_list_info(
                        products, min_month_sold
                    )
                ) if products else []
                return result
            else:
                return {
                    "totalRecords": 0,
                    "totalPage": 0,
                    "pageSize": 0,
                    "currentPage": 0,
                    "products": []
                }

    async def search_top_list_products(
            self,
            category_id: int | None = None,  # 10166 - 女装
            begin_page: int = 1,
            page_size: int = 30,
            rank_type: str = "complex"
    ):
        cache_top_list_raw_key = get_top_list_raw_cache_key(category_id, rank_type)
        cached_raw_product = await redis_async_client.get(cache_top_list_raw_key)

        if cached_raw_product and enable_top_list_raw_cache:
            raw_products = json.loads(cached_raw_product)
        else:
            if category_id:
                # Get top list products from a specific category
                raw_products_dict = await query_top_list_products(
                    site_connection_id=self.site_connection_id,
                    category_id=category_id,
                    rank_type=rank_type
                )
                raw_products = raw_products_dict.get('rankProductModels') or []
            else:
                # Get all top list products from different categories
                raw_products = await query_top_list_products_parallel(
                    site_connection_id=self.site_connection_id,
                    category_ids=top_list_category_ids,
                    rank_type=rank_type
                )
                skipped_products = remove_skipped_products(
                    raw_products, "title"
                )

                raw_products = sorted(skipped_products, key=lambda x: x.get('soldOut', False), reverse=True)

            # Only keep the fields we need. Others fields will pick up from detail info.
            filtered_raw_products = list(map(
                lambda p: {
                    'itemId': p.get('itemId'),
                    'imgUrl': p.get('imgUrl'),
                    'soldOut': p.get('soldOut'),
                    'goodsScore': p.get('goodsScore')
                },
                raw_products
            ))

            # Cache the raw products
            await redis_async_client.setex(
                cache_top_list_raw_key,
                CACHE_EXPIRATION_TOP_LIST_RAW,
                json.dumps(filtered_raw_products)
            )

        if category_id:
            paged_raw_products = raw_products
        else:
            paged_raw_products = raw_products[(begin_page - 1) * page_size: begin_page * page_size]

        item_ids = [product.get("itemId") for product in paged_raw_products]
        product_details_list = await get_product_details_with_item_ids(
            self.site_connection_id, item_ids=item_ids
        )
        product_details_transformed_list = await self.batch_transform_top_list_result_to_product_list_info(
            paged_raw_products,
            product_details_list
        )

        return jsonable_encoder(product_details_transformed_list), len(raw_products)

    async def search_variants(self, product_id):
        product = await self.alibaba_client.get_raw_product(product_id)
        result = {}
        if product:
            target_currency = self.currency
            base_currency = await self.get_base_currency()
            if product:
                variants = []
                raw_variants = product.get("productSkuInfos")
                # If there are productSkuInfos
                if raw_variants:
                    for variant in raw_variants:
                        # 获取打包零售价，然后才是净出厂价
                        price_raw = variant.get("consignPrice") or variant.get("price")
                        price_value = await convert_target_currency(
                            price_raw, target_currency=target_currency, base_currency=base_currency
                        )
                        variant = {
                            "variant_id": str(variant["skuId"]),
                            "variant_image_url": variant.get("skuAttributes", [{}])[0].get("skuImageUrl"),
                            "price": price_value,
                            **currency_utils.batch_format_currency(
                                {"price": price_value}, target_currency, locale=self.locale
                            ),
                            "stock": variant.get("amountOnSale"),
                            "mini_order_quantity": variant.get("productSaleInfo", {}).get("priceRangeList", [{}]
                                                                                          )[0].get("startQuantity"),
                            "attributes": self.get_variant_name(variant),
                        }
                        variants.append(variant)
                else:
                    product_sale_info = product.get("productSaleInfo", {})
                    price_start = product_sale_info.get("priceRangeList", [{}])[0]
                    price_raw = price_start.get("price")
                    price_value = await convert_target_currency(
                        price_raw, target_currency=target_currency, base_currency=base_currency
                    )
                    variant = {
                        "variant_id": product.get("offerId", ""),
                        "variant_image_url": product.get("productImage", {}).get("images", [])[0],
                        "price": price_value,
                        **currency_utils.batch_format_currency(
                            {"price": price_value}, target_currency, locale=self.locale
                        ),
                        "stock": product_sale_info.get("amountOnSale"),
                        "mini_order_quantity": price_start.get("startQuantity"),
                        "attributes": [
                            {"name": "名称",
                             "value": product.get("subject", "")}
                        ] if is_zh(self.language) else [
                            {"name": "name",
                             "value": product.get("subjectTrans", "")}
                        ]
                    }
                    variants.append(variant)
                result[product_id] = variants
        return jsonable_encoder(result)

    async def convert_to_products(self, products, month_sold):
        base_currency = await self.get_base_currency()
        results = []
        for product in products or []:
            if product["monthSold"] >= (month_sold or 1):
                product["title"] = product["subject"] if is_zh(self.language) else product["subjectTrans"]
                price_info = product["priceInfo"]
                price_info = await currency_utils.batch_convert_target_currency(
                    price_info, self.currency, base_currency
                )
                product["priceInfo"] = {
                    **price_info,
                    **currency_utils.batch_format_currency(price_info, self.currency, locale=self.locale)
                }
                results.append(product)
        return jsonable_encoder(results)

    async def batch_transform_top_list_result_to_product_list_info(
            self,
            product_list_raw: list,
            product_details_list: list
    ):
        """
        Transform top list products results to unified product list info format
        """
        products = []

        for product, raw in zip(product_details_list, product_list_raw):
            product_new = dict()

            product_new["item_id"] = raw["itemId"]
            product_new["title"] = product["subject"] if is_zh(self.language) else (
                remove_substrings(product["subjectTrans"]))
            product_new["main_image_url"] = raw["imgUrl"]

            product_new['category_ids'] = get_ali_category_str(product)

            product_new['price'] = await convert_target_currency(get_product_base_price(product), self.currency)

            product_new['price_format'] = format_currency(
                product_new['price'], self.currency, locale=self.locale
            )
            product_new["min_order_qty"] = product.get("minOrderQuantity")

            product_new['shipping_info'] = get_shipping_info(product, raw, is_service=True)

            # product_new["buyer_num"] = raw.get("buyerNum")
            product_new["monthly_sold"] = raw.get("soldOut")
            product_new["item_score"] = raw.get("goodsScore")

            products.append(product_new)

        return products

    async def transform_product_detail_list_info(
            self,
            product
    ):
        """
        Transform one product detail result to unified product list info format
        """
        new_prod = {k: product.get(v) for k, v in product_top_list_info_map.items()}
        new_prod["title"] = product["subject"] if is_zh(self.language) else remove_substrings(product["subjectTrans"])

        new_prod['main_image_url'] = product.get('productImage', {}).get('images', [])[0]
        new_prod['category_ids'] = get_ali_category_str(product)

        new_prod['price'] = await convert_target_currency(get_product_base_price(product), self.currency)
        new_prod['price_format'] = format_currency(
            new_prod['price'], self.currency, locale=self.locale
        )

        new_prod['shipping_info'] = get_shipping_info(product)

        return jsonable_encoder(new_prod)

    async def batch_transform_keyword_query_to_product_list_info(
            self,
            products,
            min_month_sold=0
    ):
        results = []
        for product in products:
            if check_skipped_product(product) or product.get("monthSold") < min_month_sold:
                continue

            new_prod = {k: product.get(v) for k, v in product_keyword_list_info_map.items()}

            new_prod["title"] = product["subject"] if is_zh(self.language) else remove_substrings(
                product["subjectTrans"])

            price_info = product.get("priceInfo", {})
            price = price_info.get("consignPrice") or price_info.get("price")
            new_prod['price'] = await convert_target_currency(price, self.currency)
            new_prod['price_format'] = format_currency(
                new_prod['price'], self.currency, locale=self.locale
            )

            new_prod['category_ids'] = get_ali_category_str(product)
            new_prod['min_order_qty'] = product.get("minOrderQuantity")
            new_prod['promotion_url'] = product.get("promotionURL")

            new_prod["item_score"] = product.get("sellerDataInfo", {}).get("compositeServiceScore")

            results.append(new_prod)

        return jsonable_encoder(results)

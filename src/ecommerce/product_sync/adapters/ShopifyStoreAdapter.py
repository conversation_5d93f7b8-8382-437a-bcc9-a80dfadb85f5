import asyncio
import json
from collections import defaultdict
from typing import Dict, Any, List
from urllib.parse import quote

import shopify

from ecommerce.base_api_models import ItemInput
from ecommerce.product_sync.adapters.BaseStoreAdapter import BaseStoreAdapter
from ecommerce.product_sync.product_sync_model import SupplyProductSchema, StoreProductSchema, StoreVariantSchema, \
    SupplyVariantSchema
from ecommerce.shopify.shopify_clients import ShopifyClient
from utility.api_helper import AppValueError
from utility.log import get_logger

log = get_logger(__name__)


class ShopifyStoreAdapter(BaseStoreAdapter):
    CREATE_PRODUCT_SET_MUTATION = """
        mutation createProductAsynchronous($productSet: ProductSetInput!, $synchronous: Boolean!) {
            productSet(synchronous: $synchronous, input: $productSet) {
            product {
                id,
                variants(first: 100) {
                    edges {
                        node {
                            id,
                            sku,
                            inventoryQuantity,
                            inventoryItem {
                                id
                            }
                        }
                    }
                }
            }
            userErrors {
                    code
                    field
                    message
                }
            }
        }
        """

    QUERY_PRODUCT_MUTATION = """
        query getProductVariants($productId: ID!) {
            product(id: $productId) {
                id
                title
                variants(first: 100) {
                    edges {
                        node {
                            id
                            sku,
                            inventoryQuantity,
                            inventoryItem {
                                id
                            }
                        }
                    }
                }
            }
        }
        """

    QUERY_PUBLICATIONS = """
        query {
            publications(first: 10) {
                edges {
                    node {
                        id
                        name
                    }
                }
            }
        }
    """

    PRODUCT_PUBLISH = """
        mutation productPublish($input: ProductPublishInput!) {
            productPublish(input: $input) {
                product {
                    id
                }
                userErrors {
                    field
                    message
                }
            }
        }
    """

    QUERY_INVENTORY_ITEM = """
        query inventoryItem($id: ID!) {
            inventoryItem(id: $id) {
                id
                tracked
                sku,
                inventoryLevel {
                    location {
                        id
                    }
                    available
                }
            }
        }
    """

    UPDATE_INVENTORY_SET_ON_HAND_QUANTITIES = """
        mutation inventorySetOnHandQuantities($input: InventorySetOnHandQuantitiesInput!) {
            inventorySetOnHandQuantities(input: $input) {
                userErrors {
                    field
                    message
                }
            }
        }
    """

    def __init__(self,
                 site_connection_id, org_id=None, token=None,
                 locale="en-AU;q=0.9,en;q=0.8", language="en", currency="AUD"
                 ):
        super().__init__(site_connection_id, org_id, token, locale, language, currency)
        self.shopify_client = ShopifyClient(
            site_connection_id=site_connection_id, org_id=org_id, token=token,
            locale=locale,
            language=language, currency=currency
        )
        self.location = None

    async def activate_session(self):
        try:
            shopify.ShopifyResource.activate_session(self.shopify_client.shopify_session())
            self.location = self.find_location()
        except Exception as e:
            log.exception(e)

    async def push_product_and_variants(self, supply_product: SupplyProductSchema) -> StoreProductSchema:
        await self.activate_session()
        try:
            store_product_variants = await self.get_store_product_variants(supply_product.target_product_id)
            product_input = await self._convert_to_product_payload(supply_product, store_product_variants)
            log.info(json.dumps(product_input))
            variables = {
                "productSet": product_input,
                "synchronous": True
            }

            response = shopify.GraphQL().execute(
                query=self.CREATE_PRODUCT_SET_MUTATION,
                variables=variables
            )

            result = json.loads(response) if isinstance(response, str) else response

            if "errors" in result:
                raise AppValueError(f"GraphQL API errors: {result['errors']}")

            product_create_data = result["data"]["productSet"]

            if product_create_data["userErrors"]:
                errors = "; ".join(err["message"] for err in product_create_data["userErrors"])
                raise AppValueError(f"Product creation errors: {errors}")

            shop_product = product_create_data["product"]
            store_product = await self._convert_to_product_result(shop_product)
            if supply_product.is_published:
                await self.product_publish(store_product.target_product_id)
            return store_product
        except Exception as e:
            log.exception(e)
            raise e

    async def update_variant_price_and_stock(
            self, items: list[ItemInput]
    ):
        product_items = {}
        for item in (items or []):
            variants = product_items.get(item.target_product_id)
            if not variants:
                variants = []
            variants.append(item)
            product_items[item.target_product_id] = variants

        tasks = []
        for target_product_id, items in product_items.items():
            log.info(f"update product {target_product_id} stock, items are {items}")
            tasks.append(self.update_inventory_quantity(target_product_id, items))
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            log.warning(e)

    async def update_inventory_quantity(self, target_product_id, items):
        await self.activate_session()
        inventory_item_adjustments = []
        store_product = await self.query_store_product_variants(target_product_id)
        if store_product:
            store_product_variants = {variant.target_variant_id: variant for variant in store_product.variants}
            for item in items:
                store_variant = store_product_variants.get(item.target_variant_id)
                if store_variant:
                    item.inventory_item_id = store_variant.inventory_item_id
                    inventory_item_adjustments.append({
                        "inventoryItemId": item.inventory_item_id,
                        "locationId": self.location.get("id"),
                        "quantity": item.stock_quantity
                    })
        variables = {
            "input": {
                "reason": "correction",
                "setQuantities": inventory_item_adjustments
            }
        }
        log.info(f"update inventory quantity, variables are {variables}")
        response = shopify.GraphQL().execute(
            query=self.UPDATE_INVENTORY_SET_ON_HAND_QUANTITIES,
            variables=variables
        )
        result = json.loads(response) if isinstance(response, str) else response
        log.info(f"update inventory quantity, result is {result}")
        if "errors" in result:
            log.warning(f"GraphQL API errors: {result['errors']}")

    @staticmethod
    async def _convert_to_product_result(product: dict) -> StoreProductSchema | None:
        if product:
            product_result = StoreProductSchema(target_product_id=product["id"])
            product_result.variants = [
                StoreVariantSchema(
                    target_variant_id=variant["node"]["id"],
                    inventory_item_id=variant["node"]["inventoryItem"]["id"],
                    inventory_quantity=variant["node"]["inventoryQuantity"]
                )
                for variant in product["variants"]["edges"]
            ]
            return product_result
        return None

    async def _convert_to_product_payload(
            self, supply_product: SupplyProductSchema,
            store_product_variants: dict
    ) -> Dict[str, Any]:
        store_product_id = store_product_variants.get("target_product_id")
        target_variant_map = store_product_variants.get("target_variant_map", {})
        variants_input = await self._convert_to_variants_payload(supply_product, target_variant_map)
        description = (supply_product.description or "").replace('"', "'")
        product_input = {
            "collections": [
                f"gid://shopify/Collection/{supply_product.collection_id}"
            ]
            if supply_product.collection_id else None,
            "title": supply_product.name,
            "descriptionHtml": f"{self.make_custom_attr(supply_product.custom_attr_list)}<br />{description}",
            "status": "ACTIVE" if supply_product.is_published else "DRAFT",
            "productOptions": await self.extract_product_options(supply_product.variants),
            "handle": supply_product.wd_product_id.lower() if supply_product.wd_product_id else None,
            "variants": variants_input,
            "files": await self.get_product_and_variant_media_inputs(supply_product)
        }
        if supply_product.target_product_id and store_product_id:
            product_input["id"] = supply_product.target_product_id
        return product_input

    @staticmethod
    async def get_product_and_variant_image_urls(product: SupplyProductSchema) -> List[str]:
        images = []

        if product.main_image_url:
            images.append(product.main_image_url)

        images.extend(product.sub_image_urls or [])

        if product.variants:
            for variant in product.variants:
                if variant.variant_image_url:
                    images.append(variant.variant_image_url)
                images.extend(variant.variant_images or [])

        encoded_images = [quote(url, safe=":/") for url in dict.fromkeys(images)]
        return encoded_images

    @staticmethod
    async def get_product_and_variant_media_inputs(product: SupplyProductSchema) -> List[Dict[str, Any]]:
        image_urls = await ShopifyStoreAdapter.get_product_and_variant_image_urls(product)
        if image_urls:
            return [{
                "contentType": "IMAGE",
                "originalSource": url
            } for url in image_urls]
        return []

    @staticmethod
    async def get_variant_image(variant: SupplyVariantSchema) -> Dict[str, Any]:
        if variant.variant_image_url:
            return {
                "contentType": "IMAGE",
                "originalSource": quote(variant.variant_image_url, safe=":/")
            }
        if variant.variant_images and variant.variant_images[0]:
            return {
                "contentType": "IMAGE",
                "originalSource": quote(variant.variant_images[0], safe=":/")
            }

    async def _convert_to_variants_payload(
            self, product: SupplyProductSchema, target_variant_map: dict[str, StoreVariantSchema]
    ) -> List[Dict[str, Any]]:

        variants_input = []

        for variant in product.variants:
            option_values = await self.extract_variant_options(variant.attributes)
            target_variant = target_variant_map.get(variant.target_variant_id)
            log.info(f'find target variant {target_variant}')
            variant_data = {
                "price": str(variant.rrp) if variant.rrp else None,
                # "compareAtPrice": str(variant.rrp) if variant.rrp else None,
                "barcode": variant.barcode or "",
                "inventoryItem": {
                    "tracked": True,
                    "sku": variant.sku,
                },
                "inventoryQuantities": [{
                    "quantity": int(variant.stock_quantity or 0),
                    "locationId": self.location.get("id"),
                    "name": "on_hand"
                }],
                "optionValues": option_values,
                "file": await self.get_variant_image(variant)
            }
            if target_variant:
                variant_data.pop("inventoryQuantities")
            variants_input.append(variant_data)
        return variants_input

    @staticmethod
    async def extract_product_options(variants):

        if variants is None:
            return []

        attribute_map = defaultdict(set)
        for variant in variants:
            for attr in variant.attributes:
                attribute_map[attr["attribute_name"]].add(attr["attribute_value"])

        return [
            {"name": attr_name, "values": [{"name": value} for value in sorted(values)]}
            for attr_name, values in attribute_map.items()
        ]

    @staticmethod
    async def extract_variant_options(attributes):
        return [
            {
                "name": attr["attribute_value"],
                "optionName": attr["attribute_name"]
            }
            for attr in attributes or []
        ]

    @staticmethod
    def make_custom_attr(attr_list):
        if not attr_list:
            return ""
        result = []
        for dic in attr_list:
            name = dic["name"]
            value = dic["value"]
            result.append(f"<tr><td>{name}</td><td>{value}</td></tr>")
        tr_list = "\n".join(result)
        return f"<table>{tr_list}</table>"

    @staticmethod
    def find_location():
        location = shopify.Location.find()[0]
        return {
            "id": f"gid://shopify/Location/{location.id}",
            "name": location.name
        }

    async def query_store_product_variants(self, target_product_id) -> StoreProductSchema:
        variables = {
            "productId": target_product_id
        }
        response = shopify.GraphQL().execute(
            query=self.QUERY_PRODUCT_MUTATION,
            variables=variables
        )

        result = json.loads(response) if isinstance(response, str) else response

        if "errors" in result:
            raise Exception(f"GraphQL API errors: {result['errors']}")

        return await self._convert_to_product_result(result["data"]["product"])

    async def get_store_product_variants(self, target_product_id) -> dict:
        result = {}
        if target_product_id:
            store_product = await self.query_store_product_variants(target_product_id)
            if store_product:
                result["target_product_id"] = target_product_id
                result["target_variant_map"] = {
                    variant.target_variant_id: variant for variant in store_product.variants
                }
        return result

    async def get_online_store(self):
        response = shopify.GraphQL().execute(
            query=self.QUERY_PUBLICATIONS
        )
        result = json.loads(response) if isinstance(response, str) else response
        if "errors" in result:
            raise Exception(f"GraphQL API errors: {result['errors']}")

        publications = result["data"]["publications"]["edges"]

        if publications:
            for publication in publications:
                if publication["node"]["name"] == "Online Store":
                    return publication["node"]
        return None

    async def product_publish(self, product_id):
        try:
            online_store = await self.get_online_store()
            assert online_store, f"Not find online store"
            response = shopify.GraphQL().execute(
                query=self.PRODUCT_PUBLISH,
                variables={
                    "input": {
                        "id": product_id,
                        "productPublications": [{
                            "publicationId": online_store["id"]
                        }]
                    }
                }
            )
            result = json.loads(response) if isinstance(response, str) else response
            if "errors" in result:
                log.warning(f"GraphQL API errors: {result['errors']}")

        except Exception as e:
            log.warning(str(e))

from db.pg_async_purchase_chat_items import purchase_cart_items_crud
from db.pg_async_purchase_chats import purchase_cart_crud
from db.pg_purchase_orm import PurchaseCartItem, PurchaseCart
from db.transaction_manager import not_transactional, transactional
from ecommerce.purchase.purchase_model import PurchaseCartSchema
from utility.log import get_logger

log = get_logger(__name__)


async def get_cart_item_map(cart_items):
    return {
        f"{cart_item.product_id}_{cart_item.variant_id}": cart_item for cart_item in (cart_items or [])
    }


async def save_user_purchase_cart(
        session, site_connection_id: int, user_id: int, org_id: int
):
    user_purchase_cart = await purchase_cart_crud.get_user_purchase_cart(
        session, site_connection_id, user_id
    )

    if not user_purchase_cart:
        user_purchase_cart = await purchase_cart_crud.create(
            session,
            {
                'user_id': user_id,
                'org_id': org_id,
                'site_connection_id': site_connection_id
            }
        )
    return user_purchase_cart


@not_transactional
async def query_user_purchase_cart(
        site_connection_id: int, user_id: int, variant_ids: list[str] | None, transaction_manager
) -> (PurchaseCart, PurchaseCartItem):
    user_purchase_cart = await purchase_cart_crud.get_user_purchase_cart(
        transaction_manager.session, site_connection_id, user_id
    )
    cart_items = []
    if user_purchase_cart:
        cart_items = await purchase_cart_items_crud.get_purchase_cart_items(
            transaction_manager.session, user_purchase_cart.id, variant_ids
        )
    return user_purchase_cart, cart_items


@transactional
async def add_user_purchase_cart(
        site_connection_id: int, user_id: int, org_id: int, product_id: str, cart_items: list[PurchaseCartSchema],
        transaction_manager
):
    session = transaction_manager.session
    user_purchase_cart = await save_user_purchase_cart(session, site_connection_id, user_id, org_id)

    db_cart_items = await purchase_cart_items_crud.get_purchase_cart_items(
        transaction_manager.session, user_purchase_cart.id
    )

    cart_item_map = await get_cart_item_map(db_cart_items)

    add_cart_items = []

    if cart_items:
        for cart_item in cart_items:
            cart_item_key = f"{product_id}_{cart_item.variant_id}"
            update_cart_item = cart_item_map.get(cart_item_key)
            if update_cart_item:
                cart_item.quantity += update_cart_item.quantity
                await purchase_cart_items_crud.update(
                    session, update_cart_item.id, cart_item.model_dump()
                )
            else:
                add_cart_items.append(
                    PurchaseCartItem(
                        **{'purchase_cart_id': user_purchase_cart.id, "product_id": product_id,
                           **cart_item.model_dump()}
                    )
                )
    await purchase_cart_items_crud.bulk_save(session, add_cart_items)


@transactional
async def update_user_purchase_cart(
        site_connection_id: int, user_id: int, org_id: int, product_id: str, cart_items: list[PurchaseCartSchema],
        transaction_manager
):
    session = transaction_manager.session
    user_purchase_cart = await save_user_purchase_cart(session, site_connection_id, user_id, org_id)
    db_cart_items = await purchase_cart_items_crud.get_purchase_cart_items(
        transaction_manager.session, user_purchase_cart.id
    )
    cart_item_map = await get_cart_item_map(db_cart_items)
    if cart_items:
        for cart_item in cart_items:
            cart_item_key = f"{product_id}_{cart_item.variant_id}"
            update_cart_item = cart_item_map.get(cart_item_key)
            if update_cart_item:
                if cart_item.quantity == 0:
                    await purchase_cart_items_crud.delete(session, update_cart_item.id)
                else:
                    await purchase_cart_items_crud.update(
                        session, update_cart_item.id, cart_item.model_dump()
                    )


@transactional
async def delete_user_purchase_cart(_id: int, transaction_manager):
    session = transaction_manager.session
    await purchase_cart_items_crud.delete(session, _id)
    return _id

from typing import Union

from fastapi import APIRouter, Depends, Header

import ecommerce.purchase.purchase_service as purchase_service
from ecommerce.purchase import purchase_carts_service, purchase_orchestrator
from ecommerce.purchase.purchase_model import PurchaseCartSchema, \
    UpdatePurchaseCartRequest, GetOrderPurchasePaymentAmount, \
    PurchaseOrderStatus, \
    PayCallbackSchema, ListPurchaseOrderQuery, \
    PurchaseOrderSchema, ServiceUpdatePurchaseStatusRequest, AddPurchaseCartRequest, \
    PurchaseCartTotalSchema, QueryPurchaseCartTotalSchema, GetSupplyChainVariantSchema, \
    SupplyChainVariantPurchaseDetailsSchema, SupplyChainPurchaseTotalDetails, \
    CreateSupplyChainPurchaseSchema, SupplyChainPurchaseShippingTotalDetails, \
    GetLogisticsDetailsRequest, \
    GetSupplyChainPurchaseTotalDetailsRequest, GetSupplyChainPurchaseShippingTotalDetailsRequest, \
    LogisticsDetailsSchema, PurchaseOrderDetailsModel, PurchaseOrderPaginationResponseData, \
    CancelSupplyChainSaleOrderRequest, CancelPurchaseOrderRequest, GetShopPurchaseOrderRequest, \
    GetShopPurchaseShippingTotalDetailsRequest, CreateShopPurchaseOrderRequest, AutomatedPurchaseRequest
from ecommerce.purchase.purchase_orchestrator import PurchaseOrchestrator
from utility.api_helper import SuccessResponseHandler, success_response_handler, validate_service_api_key, \
    validate_isc_service_api_key, CommonHeaders, get_common_headers
from utility.currency_utils import get_preferred_locale
from utility.exception_handler import exception_handler_decorator
from utility.log import get_logger
from utility.oauth import authenticated_user_token

log = get_logger(__name__)

router_purchase = APIRouter(prefix="/purchase", tags=["Purchase"])


@router_purchase.post("/add_user_purchase_cart", response_model=SuccessResponseHandler)
@exception_handler_decorator
async def add_user_purchase_cart(
        purchase_cart: AddPurchaseCartRequest,
        site_connection_id: int = Header(...),
        decoded_token=Depends(authenticated_user_token)
):
    user_id = decoded_token['decoded_token']['user_id']
    org_id = decoded_token['decoded_token']['org_id']
    result = await purchase_carts_service.add_user_purchase_cart(
        site_connection_id, user_id, org_id, purchase_cart.product_id, purchase_cart.cart_items
    )
    return SuccessResponseHandler(data=result, message="Successfully added purchase cart")


@router_purchase.post("/update_user_purchase_cart", response_model=SuccessResponseHandler)
@exception_handler_decorator
async def update_user_purchase_cart(
        purchase_cart: UpdatePurchaseCartRequest,
        site_connection_id: int = Header(...),
        decoded_token=Depends(authenticated_user_token)
):
    user_id = decoded_token['decoded_token']['user_id']
    org_id = decoded_token['decoded_token']['org_id']
    result = await purchase_carts_service.update_user_purchase_cart(
        site_connection_id, user_id, org_id, purchase_cart.product_id, purchase_cart.cart_items
    )
    return SuccessResponseHandler(data=result, message="Successfully updated purchase cart")


@router_purchase.get("/delete_user_purchase_cart", response_model=SuccessResponseHandler)
async def delete_user_purchase_cart(
        char_item_id: int,
        _=Depends(authenticated_user_token)
):
    result = await purchase_carts_service.delete_user_purchase_cart(char_item_id)
    return SuccessResponseHandler(data=result, message="Successfully deleted purchase cart")


@router_purchase.get("/get_user_purchase_cart",
                     response_model=SuccessResponseHandler[Union[PurchaseCartSchema, None]]
                     )
@exception_handler_decorator
async def get_user_purchase_cart(
        site_connection_id: int = Header(...),
        headers: CommonHeaders = Depends(get_common_headers),
        decoded_token=Depends(authenticated_user_token)
):
    token = decoded_token["token"]
    user_id = decoded_token['decoded_token']['user_id']
    org_id = decoded_token['decoded_token']['org_id']
    result = await PurchaseOrchestrator(
        org_id=org_id, user_id=user_id, token=token, common_headers=headers
    ).get_user_purchase_cart(
        site_connection_id
    )
    return SuccessResponseHandler(data=result, message="Successfully added purchase cart")


@router_purchase.post("/get_user_purchase_cart_total",
                      response_model=SuccessResponseHandler[Union[PurchaseCartTotalSchema, None]]
                      )
@exception_handler_decorator
async def get_user_purchase_cart_total(
        query: QueryPurchaseCartTotalSchema,
        site_connection_id: int = Header(...),
        headers: CommonHeaders = Depends(get_common_headers),
        decoded_token=Depends(authenticated_user_token)
):
    token = decoded_token["token"]
    user_id = decoded_token['decoded_token']['user_id']
    org_id = decoded_token['decoded_token']['org_id']
    result = await PurchaseOrchestrator(
        org_id=org_id, user_id=user_id, token=token, common_headers=headers
    ).get_user_purchase_cart_total(
        site_connection_id, query.variant_ids
    )
    return SuccessResponseHandler(data=result, message="Successfully added purchase cart")


@router_purchase.post(
    "/get_supply_chain_purchase_details", response_model=SuccessResponseHandler[SupplyChainVariantPurchaseDetailsSchema]
)
@exception_handler_decorator
async def get_supply_chain_purchase_details(
        products: list[GetSupplyChainVariantSchema],
        site_connection_id: int = Header(...),
        headers: CommonHeaders = Depends(get_common_headers),
        decoded_token=Depends(authenticated_user_token)
):
    token = decoded_token["token"]
    user_id = decoded_token['decoded_token']['user_id']
    org_id = decoded_token['decoded_token']['org_id']
    result = await PurchaseOrchestrator(
        org_id=org_id, user_id=user_id, token=token, common_headers=headers
    ).get_supply_chain_purchase_details(
        site_connection_id, products
    )
    return SuccessResponseHandler(data=result, message="Get purchase order from supply chain successful")


@router_purchase.post(
    "/get_supply_chain_purchase_total_details",
    response_model=SuccessResponseHandler[SupplyChainPurchaseTotalDetails]
)
@exception_handler_decorator
async def get_supply_chain_purchase_total_details(
        query: GetSupplyChainPurchaseTotalDetailsRequest,
        site_connection_id: int = Header(...),
        headers: CommonHeaders = Depends(get_common_headers),
        decoded_token=Depends(authenticated_user_token)
):
    token = decoded_token["token"]
    user_id = decoded_token['decoded_token']['user_id']
    org_id = decoded_token['decoded_token']['org_id']
    result = await PurchaseOrchestrator(
        org_id=org_id, user_id=user_id, token=token, common_headers=headers
    ).get_supply_chain_purchase_total_details(
        site_connection_id, query
    )
    return SuccessResponseHandler(data=result, message="Get purchase order from supply chain successful")


@router_purchase.post(
    "/get_supply_chain_purchase_shipping_total_details",
    response_model=SuccessResponseHandler[SupplyChainPurchaseShippingTotalDetails]
)
@exception_handler_decorator
async def get_supply_chain_purchase_shipping_total_details(
        params: GetSupplyChainPurchaseShippingTotalDetailsRequest,
        site_connection_id: int = Header(...),
        headers: CommonHeaders = Depends(get_common_headers),
        decoded_token=Depends(authenticated_user_token)
):
    token = decoded_token["token"]
    user_id = decoded_token['decoded_token']['user_id']
    org_id = decoded_token['decoded_token']['org_id']
    result = await PurchaseOrchestrator(
        org_id=org_id, user_id=user_id, token=token, common_headers=headers
    ).get_supply_chain_purchase_shipping_total_details(
        site_connection_id, params
    )
    return SuccessResponseHandler(data=result, message="Get purchase order from supply chain successful")


@router_purchase.post(
    "/get_logistics_details",
    response_model=SuccessResponseHandler[LogisticsDetailsSchema | None]
)
@exception_handler_decorator
async def get_logistics_details(
        request: GetLogisticsDetailsRequest,
        site_connection_id: int = Header(...),
        headers: CommonHeaders = Depends(get_common_headers),
        decoded_token=Depends(authenticated_user_token)
):
    token = decoded_token["token"]
    user_id = decoded_token['decoded_token']['user_id']
    org_id = decoded_token['decoded_token']['org_id']
    result = await PurchaseOrchestrator(
        org_id=org_id, user_id=user_id, token=token, common_headers=headers
    ).get_logistics_details(site_connection_id, request)
    return SuccessResponseHandler(data=result, message="Get purchase order from supply chain successful")


@router_purchase.post("/create_supply_chain_purchase_order",
                      response_model=SuccessResponseHandler[PurchaseOrderSchema])
@exception_handler_decorator
async def create_supply_chain_purchase_order(
        params: CreateSupplyChainPurchaseSchema,
        headers: CommonHeaders = Depends(get_common_headers),
        site_connection_id: int = Header(...),
        decoded_token=Depends(authenticated_user_token)
):
    token = decoded_token["token"]
    user_id = decoded_token['decoded_token']['user_id']
    org_id = decoded_token['decoded_token']['org_id']
    result = await PurchaseOrchestrator(
        org_id=org_id, user_id=user_id, token=token, common_headers=headers
    ).create_supply_chain_purchase_order(
        site_connection_id, params
    )
    return SuccessResponseHandler(data=result, message="Successfully created supply chain purchase order details")


@router_purchase.post("/get_shop_purchase_details", response_model=SuccessResponseHandler)
@exception_handler_decorator
async def get_shop_purchase_details(
        get_shop_purchase_order_request: GetShopPurchaseOrderRequest,
        headers: CommonHeaders = Depends(get_common_headers),
        decoded_token=Depends(authenticated_user_token)
):
    org_id = decoded_token['decoded_token']['org_id']
    user_id = decoded_token['decoded_token']['user_id']
    token = decoded_token['token']

    result = await PurchaseOrchestrator(
        org_id=org_id, user_id=user_id, token=token, common_headers=headers
    ).get_shop_purchase_details(get_shop_purchase_order_request)
    return SuccessResponseHandler(data=result, message="Successfully obtained shop purchase order details")


@router_purchase.post(
    "/get_shop_purchase_total_details",
    response_model=SuccessResponseHandler[SupplyChainPurchaseTotalDetails]
)
@exception_handler_decorator
async def get_shop_purchase_total_details(
        query: GetShopPurchaseOrderRequest,
        headers: CommonHeaders = Depends(get_common_headers),
        decoded_token=Depends(authenticated_user_token)
):
    token = decoded_token["token"]
    user_id = decoded_token['decoded_token']['user_id']
    org_id = decoded_token['decoded_token']['org_id']
    result = await PurchaseOrchestrator(
        org_id=org_id, user_id=user_id, token=token, common_headers=headers
    ).get_shop_purchase_total_details(query)
    return SuccessResponseHandler(data=result, message="Successfully obtained shop purchase order details")


@router_purchase.post(
    "/get_shop_logistics_details",
    response_model=SuccessResponseHandler[LogisticsDetailsSchema]
)
@exception_handler_decorator
async def get_shop_logistics_details(
        request: GetShopPurchaseOrderRequest,
        headers: CommonHeaders = Depends(get_common_headers),
        decoded_token=Depends(authenticated_user_token)
):
    token = decoded_token["token"]
    user_id = decoded_token['decoded_token']['user_id']
    org_id = decoded_token['decoded_token']['org_id']
    result = await PurchaseOrchestrator(
        org_id=org_id, user_id=user_id, token=token, common_headers=headers
    ).get_shop_logistics_details(request)
    return SuccessResponseHandler(data=result, message="Get purchase order from supply chain successful")


@router_purchase.post(
    "/get_shop_purchase_shipping_total_details",
    response_model=SuccessResponseHandler[SupplyChainPurchaseShippingTotalDetails]
)
@exception_handler_decorator
async def get_shop_purchase_shipping_total_details(
        params: GetShopPurchaseShippingTotalDetailsRequest,
        headers: CommonHeaders = Depends(get_common_headers),
        decoded_token=Depends(authenticated_user_token)
):
    token = decoded_token["token"]
    user_id = decoded_token['decoded_token']['user_id']
    org_id = decoded_token['decoded_token']['org_id']
    result = await PurchaseOrchestrator(
        org_id=org_id, user_id=user_id, token=token, common_headers=headers
    ).get_shop_purchase_shipping_total_details(params)
    return SuccessResponseHandler(data=result, message="Get purchase order from supply chain successful")


@router_purchase.post(
    "/create_shop_purchase_order", response_model=SuccessResponseHandler[PurchaseOrderSchema]
)
@exception_handler_decorator
async def create_shop_purchase_order(
        create_shop_purchase_order_request: CreateShopPurchaseOrderRequest,
        headers: CommonHeaders = Depends(get_common_headers),
        decoded_token=Depends(authenticated_user_token)
):
    token = decoded_token["token"]
    user_id = decoded_token['decoded_token']['user_id']
    org_id = decoded_token['decoded_token']['org_id']
    result = await PurchaseOrchestrator(
        org_id=org_id, user_id=user_id, token=token, common_headers=headers
    ).create_shop_purchase_order(create_shop_purchase_order_request)
    return SuccessResponseHandler(data=result, message="Successfully created shop purchase order details")


@router_purchase.post("/automated_purchase",
                      response_model=SuccessResponseHandler
                      )
@exception_handler_decorator
async def automated_purchase(
        automated_purchase_request: AutomatedPurchaseRequest,
        _=Depends(authenticated_user_token)
):
    result = await purchase_orchestrator.automated_purchase(
        automated_purchase_request.target_site_connection_id,
        automated_purchase_request.order_id,
        automated_purchase_request.order_type
    )
    return SuccessResponseHandler(data=result, message="Successfully obtained user supplier site shopping cart")


@router_purchase.post("/cancel_supply_chain_sale_order",
                      response_model=SuccessResponseHandler)
@exception_handler_decorator
async def cancel_supply_chain_sale_order(
        params: CancelSupplyChainSaleOrderRequest,
        headers: CommonHeaders = Depends(get_common_headers),
        decoded_token=Depends(authenticated_user_token)
):
    token = decoded_token["token"]
    user_id = decoded_token['decoded_token']['user_id']
    org_id = decoded_token['decoded_token']['org_id']
    result = await PurchaseOrchestrator(
        org_id=org_id, user_id=user_id, token=token, common_headers=headers
    ).cancel_supply_chain_sale_order(params)
    return SuccessResponseHandler(data=result, message="Successfully created supply chain purchase order details")


@router_purchase.post("/cancel_purchase_order", response_model=SuccessResponseHandler[CancelPurchaseOrderRequest])
@exception_handler_decorator
async def cancel_purchase_order(
        params: CancelPurchaseOrderRequest,
        _=Depends(authenticated_user_token)
):
    await purchase_service.cancel_purchase_order(params)
    return SuccessResponseHandler(data=params, message="Cancel purchase order successfully")


@router_purchase.post(
    "/query_purchase_order_list",
    response_model=SuccessResponseHandler[PurchaseOrderPaginationResponseData]
)
@exception_handler_decorator
async def query_purchase_order_list(
        purchase_order_query: ListPurchaseOrderQuery,
        headers: CommonHeaders = Depends(get_common_headers),
        decoded_token=Depends(authenticated_user_token)
) -> SuccessResponseHandler[PurchaseOrderPaginationResponseData]:
    user_id = decoded_token['decoded_token']['user_id']
    locale = get_preferred_locale(headers.locale)
    page_data = await purchase_service.query_purchase_order_list(purchase_order_query, user_id, locale)
    return SuccessResponseHandler(data=page_data, message="Query order list successful")


@router_purchase.get("/get_purchase_order", response_model=SuccessResponseHandler[PurchaseOrderSchema])
@exception_handler_decorator
async def get_purchase_order_by_id(
        purchase_order_id: int,
        _=Depends(authenticated_user_token)
):
    result = await purchase_service.get_purchase_order_by_id(purchase_order_id)
    return SuccessResponseHandler(data=result, message=f"Get purchase order from {purchase_order_id} successful")


@router_purchase.post("/service/update_purchase_order_status", response_model=SuccessResponseHandler[int])
@exception_handler_decorator
async def update_purchase_order_status(
        purchase_order_status_request: ServiceUpdatePurchaseStatusRequest,
        _=Depends(validate_isc_service_api_key)
):
    purchase_order_status_name = await purchase_service.update_purchase_order_status(
        purchase_order_status_request.wd_purchase_order_id,
        purchase_order_status_request.sale_order_id,
        purchase_order_status_request.order_status,
        purchase_order_status_request.carrier_tracking_ref
    )
    return SuccessResponseHandler(
        data=purchase_order_status_request.wd_purchase_order_id,
        message=f"Update purchase order status to {purchase_order_status_name}  successfully"
    )


@router_purchase.get(
    "/get_purchase_order_status", response_model=SuccessResponseHandler[PurchaseOrderStatus]
)
@exception_handler_decorator
async def get_purchase_order_status(
        purchase_order_id: int,
        _=Depends(authenticated_user_token)
):
    order_status = await purchase_service.query_purchase_order_status(purchase_order_id)
    return SuccessResponseHandler(
        data=order_status, message="Successfully obtained the status of the purchase order"
    )


@router_purchase.get(
    "/get_purchase_payment_amount", response_model=SuccessResponseHandler[GetOrderPurchasePaymentAmount]
)
@exception_handler_decorator
async def get_purchase_pay_amount(
        purchase_order_id: int,
        _=Depends(validate_service_api_key)
):
    try:
        payment_amount = await purchase_service.query_purchase_payment_amount(purchase_order_id)
        if payment_amount:
            return SuccessResponseHandler(data=payment_amount,
                                          message="Successfully obtained the payment amount for the purchase order")
        else:
            return success_response_handler(
                data=None, msg=f'Purchase Order not found: purchase_order_id {purchase_order_id} ', status=False
            )
    except Exception as e:
        return success_response_handler(data=None, msg=f'{str(e)}', status=False)


@router_purchase.post("/pay_callback/")
@exception_handler_decorator
async def pay_callback(pay_callback_request: PayCallbackSchema, _=Depends(validate_service_api_key)):
    await purchase_orchestrator.purchase_pay_callback(
        pay_callback_request.purchase_order_id,
        pay_callback_request.payment_status,
    )
    return SuccessResponseHandler(data=None, message="Order payment status successful")


@router_purchase.get("/get_purchase_details", response_model=SuccessResponseHandler[PurchaseOrderDetailsModel])
@exception_handler_decorator
async def get_purchase_details(
        purchase_order_id: int,
        headers: CommonHeaders = Depends(get_common_headers),
        _=Depends(authenticated_user_token)
):
    locale = get_preferred_locale(headers.locale)
    result = await purchase_service.query_purchase_details(purchase_order_id, locale)
    return SuccessResponseHandler(data=result, message=f"Get purchase order from {purchase_order_id} successful")

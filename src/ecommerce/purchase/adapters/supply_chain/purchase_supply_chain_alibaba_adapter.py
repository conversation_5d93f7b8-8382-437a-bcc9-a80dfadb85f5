import json
from decimal import Decimal

from fastapi.encoders import jsonable_encoder

from ecommerce import factory
from ecommerce.alibaba.alibaba_clients.model import Ali1688OrderCreateInfo, Ali1688OrderCreateInfoParam, \
    get_receiver_address, ali_1688_invoice_default, Ali1688OrderPreviewInfo, Ali1688CargoParam, Ali1688Invoice, \
    Ali1688CreateOrderPreviewParam
from ecommerce.product.common_api import get_exchange_rate, get_vendor_code
from ecommerce.purchase.adapters.supply_chain.purchase_supply_chain_adapter import PurchaseSupplyChainAdapter
from supplier.affiliates.alibaba.ali_data_models import SyncedProductsInfo
from supplier.affiliates.alibaba.ali_data_service import ali_api_call, \
    get_product_details_with_item_ids
from supplier.affiliates.alibaba.endpoint_alibaba_order import api_namespace_order
from supplier.affiliates.alibaba.isc_data_load import ali_product_details_batch_etl
from utility import decimal_utils
from utility.log import get_logger

log = get_logger(__name__)


class PurchaseSupplyChainAlibabaAdapter(PurchaseSupplyChainAdapter):

    async def fetch_product(self, product_id: str):
        pass

    def __init__(self, site_connection_id, org_id, token):
        super().__init__(site_connection_id, org_id, token)
        self.alibaba_client = factory.get_eshop(site_connection_id=site_connection_id, token=token)

    async def get_exchange_rate(self, target_currency: str):
        base_currency = await self.get_base_currency()
        return await get_exchange_rate(target_currency, base_currency)

    async def get_base_currency(self):
        return "CNY"

    def get_variant_name(self, variant):
        variant_names = []
        attributes = variant.get("attributes")
        if attributes:
            for attr in attributes:
                name, value = attr
                variant_names.append(f"{name}:{value}")
            return ", ".join(variant_names)
        return ""

    async def fetch_products(self, product_ids: list[str], query_product_variants_map: dict = None):
        synced_products_info = SyncedProductsInfo(product_ids=[int(product_id) for product_id in product_ids])
        supply_products = await get_product_details_with_item_ids(
            self.site_connection_id, synced_products_info.product_ids
        )
        assert supply_products, f"Failed to fetch product {product_ids}"

        vendor_code = get_vendor_code(site_type=self.site_type, buyer_id=self.website_info.site_id)
        log.info(f"Loading product details from vendor_code {vendor_code}...")
        product_details_list = await ali_product_details_batch_etl(
            vendor_code=vendor_code,
            product_details=supply_products
        )

        return product_details_list

    # async def fetch_product(self, product_id: str):
    #     return await self.alibaba_client.get_product(product_id)

    async def create_order_preview(self, supply_chain_products):
        split_supply_chain_variants_map = await self.split_supply_chain_variants_by_seller(supply_chain_products)
        results = []
        for seller_open_id, order_items in split_supply_chain_variants_map.items():
            create_order_info = await self.transform_order_preview_params(order_items)
            result = await self.create_alibaba_order_preview(create_order_info)
            results.append(result)

        log.info(results)

        fail_results = [
            result.get("errorMsg") for result in results if not result.get("success", False)
        ]

        assert len(fail_results) == 0, ",".join(fail_results)

        total_details = {
            "items_subtotal": Decimal("0.00"),
            "domestic_shipping": Decimal("0.00"),
            "domestic_total": Decimal("0.00"),
        }
        for result in results:
            order_review_result = result.get("orderPreviewResuslt")  # 1688 接口返回 为 orderPreviewResuslt
            for order_review in order_review_result:
                total_details["domestic_total"] = decimal_utils.decimal_add(
                    total_details["domestic_total"],
                    order_review.get("sumPayment")
                )
                total_details["domestic_shipping"] = decimal_utils.decimal_add(
                    total_details["domestic_shipping"],
                    order_review.get("sumCarriage")
                )
                total_details["items_subtotal"] = decimal_utils.decimal_add(
                    total_details["items_subtotal"],
                    order_review.get("sumPaymentNoCarriage")
                )
        return {
            key: decimal_utils.decimal_divide(value, 100)
            for key, value in total_details.items()
        }

    async def create_sale_orders(self, purchase_order):
        seller_order_map = await self.split_purchase_order_items_by_seller(purchase_order)
        create_order_results = []
        results = []
        for seller_open_id, order_items in seller_order_map.items():
            order_lines = jsonable_encoder(order_items)
            create_order_info = await self.transform_sale_orders_params(purchase_order, order_lines)
            create_order_result = await self.create_alibaba_order(create_order_info)
            create_order_result.update(
                {
                    "seller_open_id": seller_open_id,
                    "order_lines": order_lines
                }
            )
            create_order_results.append(create_order_result)

        fail_results = [
            result.get("message") for result in create_order_results if not result.get("success", False)
        ]

        assert len(fail_results) == 0, ",".join(fail_results)

        for create_order_result in create_order_results:
            result = create_order_result.get("result", {})
            domestic_total = decimal_utils.decimal_divide(result.get("totalSuccessAmount"), 100)
            domestic_shipping = decimal_utils.decimal_divide(result.get("postFee"), 100)
            items_subtotal = decimal_utils.decimal_subtract(domestic_total, domestic_shipping)
            results.append({
                "purchase_order_info": {
                    "purchase_order_id": purchase_order.id,
                    "seller_open_id": create_order_result.get("seller_open_id"),
                    "supplier_order_id": result.get("orderId"),
                    "order_lines": create_order_result.get("order_lines")
                },
                "purchase_total_details": {
                    "domestic_total": domestic_total,
                    "domestic_shipping": domestic_shipping,
                    "items_subtotal": items_subtotal
                }
            })
        return results

    @staticmethod
    async def transform_invoice_param(invoice_address):
        return Ali1688Invoice(
            provinceText=invoice_address.get("state_name"),
            cityText=invoice_address.get("city"),
            post_code=invoice_address.get("zip"),
            address=f"{invoice_address.get('street', '')} {invoice_address.get('street2', '')}".strip(),
            fullName=invoice_address.get("name", ""),
            phone=invoice_address.get("phone", ""),
            mobile=invoice_address.get("mobile", "")
        )

    @staticmethod
    async def transform_cargo_params(order_items: list):
        return [
            Ali1688CargoParam(**{
                "specId": order_item.get("specific_id"),
                "quantity": order_item.get("quantity"),
                "offerId": order_item.get("product_id")
            })
            for order_item in order_items
        ]

    async def transform_order_preview_params(self, order_items):
        return Ali1688OrderPreviewInfo(
            **{
                "cargo_params": await self.transform_cargo_params(
                    order_items
                )
            }
        )

    async def transform_sale_orders_params(self, purchase_order, order_items):
        return Ali1688OrderCreateInfo(
            **{
                "cargo_params": await self.transform_cargo_params(
                    order_items
                ),
                "out_order_id": str(purchase_order.id)
            }
        )

    async def create_alibaba_order(self, order_create_info: Ali1688OrderCreateInfo):
        api_name = "alibaba.trade.createCrossOrder"
        create_order_info = Ali1688OrderCreateInfoParam(
            flow=order_create_info.flow,
            message=order_create_info.message,
            isvBizType=order_create_info.is_bis_type,

            addressParam=get_receiver_address(order_create_info.receiver_address_id),
            cargoParamList=order_create_info.cargo_params,
            invoiceParam=ali_1688_invoice_default,

            tradeType=order_create_info.trade_type,
            shopPromotionId=order_create_info.shop_promotion_id,

            anonymousBuyer=order_create_info.anonymous_buyer,
            fenxiaoChannel=order_create_info.fenxiao_channel,
            inventoryMode=order_create_info.inventory_mode,
            outOrderId=order_create_info.out_order_id,
            pickupService=order_create_info.pickup_service,

            warehouseCode=order_create_info.warehouse_code,
            preSelectPayChannel=order_create_info.pre_select_payChannel,
            smallProcurement=order_create_info.small_procurement
        )

        create_order_dict = create_order_info.model_dump(exclude_none=True)

        for key, value in create_order_dict.items():
            if type(value) is dict:
                create_order_dict[key] = json.dumps(
                    value, ensure_ascii=False
                ).replace(" ", "")

        cargo_param_list = create_order_dict["cargoParamList"]
        create_order_dict["cargoParamList"] = json.dumps(
            cargo_param_list, ensure_ascii=False
        ).replace(" ", "")
        log.info(f"create_order_preview_dict: {create_order_dict}")

        create_order_result = await ali_api_call(
            api_namespace=api_namespace_order,
            api_name=api_name,
            site_connection_id=self.site_connection_id,
            payload=create_order_dict,
            method="POST"
        )

        return create_order_result

    async def create_alibaba_order_preview(self, order_preview_info: Ali1688OrderPreviewInfo):
        api_name = "alibaba.createOrder.preview"
        create_order_preview = Ali1688CreateOrderPreviewParam(
            addressParam=get_receiver_address(order_preview_info.receiver_address_id),
            cargoParamList=order_preview_info.cargo_params,
            invoiceParam=ali_1688_invoice_default,
            flow=order_preview_info.flow,
            instanceId=order_preview_info.instance_id,
            encryptOutOrderInfo=order_preview_info.encryptOutOrderInfo,
            proxySettleRecordId=order_preview_info.proxy_settle_record_id,
            inventoryMode=order_preview_info.inventory_mode,
            outOrderId=order_preview_info.out_order_id,
            pickupService=order_preview_info.pickup_service
        )
        create_order_preview_dict = create_order_preview.model_dump(exclude_none=True)
        for key, value in create_order_preview_dict.items():
            if type(value) is dict:
                create_order_preview_dict[key] = json.dumps(
                    value, ensure_ascii=False
                ).replace(" ", "")

        cargo_param_list = create_order_preview_dict["cargoParamList"]
        create_order_preview_dict["cargoParamList"] = json.dumps(
            cargo_param_list, ensure_ascii=False
        ).replace(" ", "")
        log.info(f"create_order_preview_dict: {create_order_preview_dict}")

        create_order_preview_call = await ali_api_call(
            api_namespace=api_namespace_order,
            api_name=api_name,
            site_connection_id=self.site_connection_id,
            payload=create_order_preview_dict,
            method="POST"
        )
        return create_order_preview_call

    async def cancel_alibaba_order(self, sale_id, cancel_reason, remark):
        api_name = "alibaba.trade.cancel"
        cancel_alibaba_order_params = {
            "webSite": "alibaba",
            "tradeID": sale_id,
            "cancelReason": cancel_reason or "other",
            "remark": remark or ""
        }
        log.info(f"cancel_alibaba_order_params: {cancel_alibaba_order_params}")
        cancel_alibaba_order_call = await ali_api_call(
            api_namespace=api_namespace_order,
            api_name=api_name,
            site_connection_id=self.site_connection_id,
            payload=cancel_alibaba_order_params,
            method="POST"
        )
        log.info(cancel_alibaba_order_call)
        return cancel_alibaba_order_call

    async def cancel_sale_order(self, sale_id, cancel_reason, remark):
        result = await self.cancel_alibaba_order(sale_id, cancel_reason, remark)
        assert result.get("success", False), result.get("errorMessage")
        return result

from babel.numbers import format_currency
from fastapi.encoders import jsonable_encoder

from db.pg_helps import object_as_dict_advanced
from db.pg_purchase_orm import PurchaseCartItem
from db.pg_site_connection import get_website_info, get_website_role_type, get_site_connection_info
from db.pg_site_provider_info import get_site_provider_info_by_id
from ecommerce.product.common_api import get_exchange_rate, AIOrderStatus
from ecommerce.purchase import purchase_carts_service, purchase_service
from ecommerce.purchase.adapters.shop_store.purchase_shop_store_shopify_adapter import PurchaseShopStoreShopifyAdapter
from ecommerce.purchase.adapters.supply_chain.purchase_supply_chain_alibaba_adapter import \
    PurchaseSupplyChainAlibabaAdapter
from ecommerce.purchase.adapters.supply_chain.purchase_supply_chain_isc_adapter import PurchaseSupplyChainISCAdapter
from ecommerce.purchase.purchase_model import PurchaseCartSchema, PurchaseCartTotalSchema, GetSupplyChainVariantSchema, \
    PurchaseCartProductSchema, SupplyChainVariantPurchaseDetailsSchema, \
    SupplyChainPurchaseTotalDetails, CreateSupplyChainPurchaseSchema, PurchaseOrderSchema, \
    GetLogisticsDetailsRequest, GetSupplyChainPurchaseShippingTotalDetailsRequest, \
    SupplyChainPurchaseShippingTotalDetails, \
    GetSupplyChainPurchaseTotalDetailsRequest, LogisticsDetailsSchema, CancelSupplyChainSaleOrderRequest, \
    GetShopPurchaseOrderRequest, CreateShopPurchaseOrderRequest, CalcLogisticsRouteCostRequest
from ecommerce.purchase.purchase_model import PurchaseProductSchema
from ecommerce.purchase.purchase_order_enum import PurchaseOrderTypeEnum, PurchaseOrderStatusEnum, \
    ErpOrderPayStatusEnum, SupplyChainSaleOrderStatusEnum
from ecommerce.warpdrive.warpdriven_erp_client import get_account_discount_by_service_key, \
    get_delivery_address, get_account_config_by_service_key, \
    get_invoice_address_by_service_key, calc_logistics_route_cost_post_by_service_key
from utility import decimal_utils, currency_utils
from utility.api_helper import CommonHeaders
from utility.currency_utils import get_preferred_locale
from utility.log import get_logger

log = get_logger(__name__)


class PurchaseOrchestrator:
    _supply_adapter = {
        "1688": PurchaseSupplyChainAlibabaAdapter,
        "ISCPlatform_Italy": PurchaseSupplyChainISCAdapter
    }

    _store_adapter = {
        "Shopify": PurchaseShopStoreShopifyAdapter
    }

    def __init__(
            self, org_id: int, user_id: int, token: str | None = None, common_headers: CommonHeaders | None = None
    ):
        self.account_config = None
        self.is_purchase_free = None
        self.is_shipping_free = None
        self.is_auto_drop_shipping = None
        self.gst_percentage = None
        self.fixed_service_fee = None
        self.service_fee_type = None
        self.commission_rate_percentage = None
        self.commission_rate = None
        self.base_currency = None
        self.supply_adapter = None
        self.store_adapter = None
        self.exchange_rate = None
        self.org_id = org_id
        self.user_id = user_id
        self.token = token
        self.language = common_headers.x_user_language
        if common_headers.locale:
            self.locale = get_preferred_locale(common_headers.locale)
        self.target_currency = common_headers.x_user_currency
        self.account_discount = None

    @staticmethod
    def get_site_provider_info(site_connection_id: int):
        website_info = get_website_info(site_connection_id)
        assert website_info, "Failed to get website info"
        site_type = website_info.site_type
        site_provider_info = get_site_provider_info_by_id(site_type)
        assert site_provider_info, "Failed to get site provider info"
        role_type = get_website_role_type(site_connection_id)
        return website_info, site_provider_info, role_type

    async def init_supply_adapter(self, source_site_connection_id: int, org_id: int, token: str):
        website_info, site_provider_info, role_type = self.get_site_provider_info(
            source_site_connection_id
        )
        issuer = site_provider_info.get("issuer")
        supply_adapter_class = self._supply_adapter.get(issuer)
        assert supply_adapter_class, f"Adapter not found for {issuer}"
        self.supply_adapter = supply_adapter_class(source_site_connection_id, org_id, token)
        self.supply_adapter.init(website_info, site_provider_info, role_type)
        self.base_currency = await self.supply_adapter.get_base_currency()
        self.exchange_rate = await get_exchange_rate(self.target_currency, self.base_currency)
        self.account_config = await self.init_supply_account_config()

    async def init_supply_account_discount(self, amount):
        site_type = int(self.supply_adapter.site_type)
        self.account_discount = await get_account_discount_by_service_key(
            site_type, self.user_id, amount, self.target_currency
        )
        self.service_fee_type = self.account_discount.get("service_fee_type", "fixed")
        self.fixed_service_fee = self.account_discount.get("fixed_service_fee", 0.0)
        self.gst_percentage = self.account_discount.get("gst", 0.0)
        self.commission_rate = self.account_discount.get("discount") or 1.0
        self.commission_rate_percentage = f"{self.account_discount.get('discount_percentage') or '0.00'}"

    async def init_supply_account_config(self):
        site_type = int(self.supply_adapter.site_type)
        self.account_config = await get_account_config_by_service_key(
            site_type, self.user_id
        )
        self.is_auto_drop_shipping = self.account_config.get("is_auto_dropshipping", False)
        self.is_shipping_free = self.account_config.get("is_shipping_free", False)
        self.is_purchase_free = self.account_config.get("is_purchase_free", False)

    async def init_store_adapter(self, target_site_connection_id: int, org_id: int, token: str):
        website_info, site_provider_info, role_type = self.get_site_provider_info(
            target_site_connection_id
        )
        issuer = site_provider_info.get("issuer")
        store_adapter_class = self._store_adapter.get(issuer)
        assert store_adapter_class, f"Adapter not found for {issuer}"
        self.store_adapter = store_adapter_class(target_site_connection_id, org_id, token)
        self.store_adapter.init(website_info, site_provider_info, role_type)

    async def reset_target_currency(self, target_currency):
        self.target_currency = target_currency

    @staticmethod
    async def group_variants_by_product(cart_items: list[PurchaseCartItem | GetSupplyChainVariantSchema]):
        cart_product_items = {}
        for cart_item in cart_items:
            cart_product_items.setdefault(cart_item.product_id, []).append(cart_item)
        return cart_product_items

    @staticmethod
    def get_supply_chain_product_map(supply_products):
        return {
            product.get("vendor_product_id"): product for product in (supply_products or [])
        }

    @staticmethod
    def get_supply_chain_product_variants_map(supply_product):
        return {
            f"{supply_product.get('vendor_product_id')}_{variant.get('vendor_variant_id')}": variant for
            variant in supply_product.get("variants", [])
        }

    async def calculate_parcel_info(self, supply_products: list):
        parcel_info = {
            "weight": decimal_utils.to_decimal(precision=0),
            "parcel_value": decimal_utils.to_decimal(precision=2),
        }
        if supply_products:
            supply_variants = [variant for product in supply_products for variant in product.get("variants", [])]
            for variant in supply_variants:
                parcel_info["weight"] = decimal_utils.decimal_add(
                    parcel_info["weight"],
                    decimal_utils.decimal_multiply(
                        variant.get("parcel_info", {}).get("weight", 0.00), variant.get("quantity"), 0
                    )
                )
                parcel_info["parcel_value"] = decimal_utils.decimal_add(
                    parcel_info["parcel_value"], variant["total"]
                )
        return {
            **decimal_utils.batch_to_decimal(parcel_info, 2),
            "parcel_value_currency": self.base_currency,
        }

    async def filter_supply_chain_product_and_variants(self, cart_product_items, supply_products):
        products = []
        supply_chain_product_map = self.get_supply_chain_product_map(supply_products)
        for product_id, cart_items in cart_product_items.items():
            supply_chain_product = supply_chain_product_map.get(product_id)
            if supply_chain_product and cart_items and len(cart_items) > 0:
                variants = []
                supply_chain_product_variants_map = self.get_supply_chain_product_variants_map(
                    supply_chain_product
                )
                for cart_item in cart_items:
                    cart_item_key = f"{product_id}_{cart_item.variant_id}"
                    variant = supply_chain_product_variants_map.get(cart_item_key)
                    if variant:
                        price = variant.get("standard_price")
                        total = decimal_utils.decimal_multiply(price, cart_item.quantity)
                        order_price = decimal_utils.to_decimal(variant.get("order_price"), precision=2)
                        order_total = decimal_utils.to_decimal(decimal_utils.decimal_multiply(
                            order_price, cart_item.quantity
                        ), precision=2)
                        variant_price_info = {
                            "price": price,
                            "total": total,
                            "order_price": order_price,
                            "order_total": order_total
                        }
                        variant_price_format_dict = currency_utils.batch_format_currency(
                            variant_price_info, self.target_currency, locale=self.locale
                        )
                        logistics_weight = decimal_utils.decimal_multiply(
                            variant.get("logistics", {}).get("weight", 0), 1000, precision=3
                        )
                        variant_dict = {
                            **variant_price_info,
                            **variant_price_format_dict,
                            "product_id": product_id,
                            "variant_id": variant.get("vendor_variant_id"),
                            "specific_id": variant.get("specific_id"),
                            "variant_name": self.supply_adapter.get_variant_name(variant),
                            "variant_image": variant.get("variant_image_url"),
                            "stock": variant.get("stock_quantity"),
                            "currency": self.target_currency,
                            "quantity": cart_item.quantity,
                            "parcel_info": {
                                **variant.get("dimensions", {}),
                                **{"weight": logistics_weight or 100}
                            }
                        }
                        if hasattr(cart_item, "id"):
                            variant_dict["cart_item_id"] = cart_item.id

                        variants.append(
                            variant_dict
                        )
                products.append(
                    {
                        "product_id": product_id,
                        "product_name": supply_chain_product.get("name"),
                        "product_image": supply_chain_product.get("main_image_url"),
                        "min_order_qty": supply_chain_product.get("min_order_qty"),
                        "seller_open_id": supply_chain_product.get("seller_open_id"),
                        "variants": variants
                    }
                )
        return products

    async def query_supply_chain_product_variants(self, site_connection_id, query_variants):
        await self.init_supply_adapter(site_connection_id, self.org_id, self.token)
        product_ids = list({variant.product_id for variant in query_variants})
        cart_product_items = await self.group_variants_by_product(query_variants)
        supply_products = await self.supply_adapter.fetch_products(product_ids, cart_product_items)
        return await self.filter_supply_chain_product_and_variants(cart_product_items, supply_products)

    async def get_user_purchase_cart_total(self, site_connection_id: int, variant_ids):
        user_purchase_cart, products = await self.get_user_purchase_cart_items(site_connection_id, variant_ids)

        if not user_purchase_cart:
            return None

        total = decimal_utils.to_decimal(0)
        if products:
            for product in products:
                for variant in product.variants:
                    total = decimal_utils.decimal_add(total, variant.total)
        return PurchaseCartTotalSchema(**{
            **user_purchase_cart,
            "total": total,
            "total_format": format_currency(total, self.target_currency, locale=self.locale),
            "currency": self.target_currency
        })

    async def get_user_purchase_cart_items(self, site_connection_id: int, variant_ids):
        user_purchase_cart, cart_items = await purchase_carts_service.query_user_purchase_cart(
            site_connection_id, self.user_id, variant_ids
        )
        if not user_purchase_cart:
            return None, []

        products = []
        if cart_items and len(cart_items) > 0:
            products = await self.query_supply_chain_product_variants(site_connection_id, cart_items)
            products = [PurchaseCartProductSchema(**product) for product in products]
        user_purchase_cart = object_as_dict_advanced(user_purchase_cart)
        return user_purchase_cart, products

    async def get_user_purchase_cart(self, site_connection_id: int) -> PurchaseCartSchema | None:
        user_purchase_cart, products = await self.get_user_purchase_cart_items(site_connection_id, None)
        if not user_purchase_cart:
            return None

        return PurchaseCartSchema(**{**user_purchase_cart, "products": products})

    async def convert_logistics_info(
            self, delivery_address, parcel_info: dict, provider_type: str, automatic_type: str = "shortest_time"
    ) -> dict:
        delivery_address = jsonable_encoder(delivery_address)
        recipient_info = {
            "country_code": delivery_address.get("country_code"),
            "post_code": delivery_address.get("zip"),
        } if delivery_address else None

        sender_info = {
            "country_code": "CN",
            "post_code": "1010101",
        }
        return {
            "include_battery": False,
            "recipient_info": recipient_info,
            "sender_info": sender_info,
            "parcel_info": parcel_info,
            "currency_code": self.target_currency,
            "provider_type": provider_type,
            "automatic_type": automatic_type
        }

    async def get_logistics_info(self, supply_products, delivery_address):
        parcel_info = await self.calculate_parcel_info(supply_products)
        logistics_info = await self.convert_logistics_info(delivery_address, parcel_info, "automatic")
        return jsonable_encoder(logistics_info)

    async def get_logistics_details(self, site_connection_id, params: GetLogisticsDetailsRequest):
        delivery_address = params.delivery_address
        try:
            delivery_address = delivery_address or await self.get_delivery_address()
        except Exception as e:
            log.warning(e)
            return None
        supply_products = await self.query_supply_chain_product_variants(
            site_connection_id, params.variants
        )
        parcel_info = await self.calculate_parcel_info(supply_products)
        logistics_info = await self.convert_logistics_info(delivery_address, parcel_info, "automatic")
        return LogisticsDetailsSchema(**{
            "delivery_address": delivery_address,
            "logistics_info": logistics_info,
        })

    async def calc_logistics_route_cost(self, logistics_route_info, logistics_info):
        return await calc_logistics_route_cost_post_by_service_key(
            logistics_route_info.provider_type,
            logistics_route_info.route_code,
            logistics_info["parcel_info"],
            logistics_info["recipient_info"],
            logistics_info["sender_info"],
            self.target_currency
        )

    async def get_supply_chain_purchase_details(
            self, site_connection_id, supply_chain_variants: list[GetSupplyChainVariantSchema]
    ):
        supply_products = await self.query_supply_chain_product_variants(site_connection_id, supply_chain_variants)
        products = [PurchaseProductSchema(**product) for product in supply_products]
        return SupplyChainVariantPurchaseDetailsSchema(**{
            "products": products
        })

    @staticmethod
    async def compute_supply_chain_purchase_total_details(
            purchase_total_details, service_fee_type, fixed_service_fee, commission_rate, gst_percentage
    ):
        items_subtotal = purchase_total_details.get("items_subtotal")
        if service_fee_type == "fixed":
            service_fee = fixed_service_fee
        else:
            service_fee = decimal_utils.decimal_multiply(
                items_subtotal, decimal_utils.decimal_subtract(
                    commission_rate, 1.00
                )
            )

        service_fee_gst = decimal_utils.decimal_multiply(
            service_fee, gst_percentage
        )

        total = decimal_utils.decimal_add(
            purchase_total_details.get("domestic_total"), service_fee
        )
        promotion_applied = 0.00
        total = decimal_utils.decimal_add(total, promotion_applied)
        total = decimal_utils.decimal_add(total, service_fee_gst)

        purchase_total_details = {
            **purchase_total_details,
            "service_fee": service_fee,
            "service_fee_gst": service_fee_gst,
            "promotion_applied": promotion_applied,
            "total": total
        }
        return purchase_total_details

    async def compute_supply_chain_main_purchase_total_details(self, supply_chain_products):
        purchase_total_details = await self.supply_adapter.create_order_preview(
            supply_chain_products
        )
        purchase_total_details = currency_utils.batch_convert_exchange_rate(
            purchase_total_details, self.exchange_rate
        )
        items_subtotal = purchase_total_details.get("items_subtotal")
        await self.init_supply_account_discount(items_subtotal)
        purchase_total_details = await self.compute_supply_chain_purchase_total_details(
            purchase_total_details, self.service_fee_type, self.fixed_service_fee, self.commission_rate,
            self.gst_percentage
        )
        return purchase_total_details

    async def get_supply_chain_purchase_total_details(
            self, site_connection_id, params: GetSupplyChainPurchaseTotalDetailsRequest
    ):
        supply_chain_variants = params.variants
        supply_chain_products = await self.query_supply_chain_product_variants(
            site_connection_id, supply_chain_variants
        )
        purchase_total_details = await self.compute_supply_chain_main_purchase_total_details(
            supply_chain_products
        )
        purchase_total_details_format = currency_utils.batch_format_currency(
            purchase_total_details, self.target_currency, locale=self.locale
        )
        return SupplyChainPurchaseTotalDetails(
            **{
                **purchase_total_details,
                **purchase_total_details_format,
                "service_fee_type": self.service_fee_type,
                "commission_rate_percentage": f"{self.commission_rate_percentage}",
                "gst_percentage": decimal_utils.decimal_multiply(self.gst_percentage, 100, 2)
            }
        )

    async def compute_supply_chain_purchase_logistics_details(
            self, supply_chain_products, logistics_route_info, delivery_address
    ):
        logistics_fee = 0
        delivery_address = jsonable_encoder(delivery_address)
        logistics_info = await self.get_logistics_info(supply_chain_products, delivery_address)
        logistics_info["parcel_info"] = jsonable_encoder(logistics_route_info.parcel_info)
        logistics_route = {
            "code": logistics_route_info.route_code,
            "name": logistics_route_info.route_code,
            "sum_fee": 0,
            "currency": self.target_currency,
            "provider_type": logistics_route_info.provider_type,
            "estimated_time": "0",
            "support_package_merge": False
        }
        if not self.is_shipping_free:
            logistics_route = await self.calc_logistics_route_cost(logistics_route_info, logistics_info)
            logistics_fee = logistics_route.get("sum_fee", 0.00)
        return logistics_fee, logistics_info, logistics_route

    async def get_supply_chain_purchase_shipping_total_dict(
            self, supply_chain_products, logistics_fee
    ):

        purchase_total_details = await self.compute_supply_chain_main_purchase_total_details(
            supply_chain_products
        )
        subtotal_before_international_shipping = purchase_total_details.get("total")
        estimated_international_shipping = logistics_fee
        order_total = decimal_utils.decimal_add(
            subtotal_before_international_shipping, estimated_international_shipping
        )
        purchase_total_details = {
            **purchase_total_details,
            "subtotal_before_international_shipping": subtotal_before_international_shipping,
            "estimated_international_shipping": estimated_international_shipping,
            "order_total": order_total
        }
        return purchase_total_details

    async def get_supply_chain_purchase_shipping_total_details(
            self, site_connection_id, params: GetSupplyChainPurchaseShippingTotalDetailsRequest
    ):
        supply_chain_variants = params.variants
        delivery_address = params.delivery_address or await self.get_delivery_address()
        supply_chain_products = await self.query_supply_chain_product_variants(
            site_connection_id, supply_chain_variants
        )
        logistics_fee, logistics_info, logistics_route = await self.compute_supply_chain_purchase_logistics_details(
            supply_chain_products, params.logistics_route_info, delivery_address
        )
        purchase_total_details = await self.get_supply_chain_purchase_shipping_total_dict(
            supply_chain_products, logistics_fee
        )
        purchase_total_details_format = currency_utils.batch_format_currency(
            purchase_total_details, self.target_currency, locale=self.locale
        )
        return SupplyChainPurchaseShippingTotalDetails(
            **{
                **purchase_total_details,
                **purchase_total_details_format,
                "commission_rate_percentage": f"{self.commission_rate_percentage}",
                "gst_percentage": decimal_utils.decimal_multiply(self.gst_percentage, 100, 2)
            }
        )

    @staticmethod
    async def convert_invoice_address(invoice_address: dict) -> dict:
        return {
            "name": invoice_address.get("full_name") or invoice_address.get("name", ""),  # 使用 full_name，否则用 name
            "email": invoice_address.get("email", ""),  # 邮箱
            "mobile": invoice_address.get("mobile") or "12345678",  # 手机
            "phone": invoice_address.get("phone") or "010-1234",  # 电话
            "zip": invoice_address.get("zip") or "1234",  # 邮政编码
            "country_code": "AU",  # 国家代码映射
            "state_name": "New South Wales",  # 州/省份名称映射
            "city": invoice_address.get("city") or "Sydney",  # 城市
            "street": invoice_address.get("street") or "",  # 街道地址
            "street2": invoice_address.get("street2") or "",  # 街道地址2
        }

    @staticmethod
    async def convert_delivery_address(shipping):
        return {
            "name": f"{shipping.get('first_name', '')} {shipping.get('last_name', '')}".strip(),
            "email": "",  # `shipping` 中没有 email 字段，可根据需求补充
            "mobile": shipping.get("phone") or "15555555555",
            "phone": shipping.get("phone") or "15555555555",
            "zip": shipping.get("postcode", ""),
            "country_code": shipping.get("country", ""),
            "state_name": shipping.get("state", ""),
            "city": shipping.get("city", ""),
            "street": shipping.get("address_1", ""),
            "street2": shipping.get("address_2", "")
        }

    async def get_invoice_address(self):
        invoice_address = await get_invoice_address_by_service_key(self.user_id)
        assert invoice_address, "Please set the invoice address"
        return await self.convert_invoice_address(invoice_address)

    async def get_delivery_address(self):
        delivery_address = await get_delivery_address(self.token)
        assert delivery_address, "Please set the delivery address"
        return delivery_address

    async def covert_purchase_order(
            self, purchase_total_details, delivery_address, logistics_info, logistics_route_info, logistics_route,
            invoice_address
    ):
        payment_currency = self.target_currency
        payment_currency_rate = await get_exchange_rate(payment_currency, self.target_currency)
        order_total = purchase_total_details.get("order_total")
        payment_amount = decimal_utils.to_decimal(
            decimal_utils.decimal_multiply(
                order_total, payment_currency_rate, 3
            )
        )
        return jsonable_encoder(
            {
                "org_id": self.org_id,
                "user_id": self.user_id,
                "source_site_connection_id": self.supply_adapter.site_connection_id,
                "supplier_id": self.supply_adapter.site_type,
                "supplier_name": self.supply_adapter.website_info.name,
                "supplier_contact": self.supply_adapter.website_info.domain,
                "supplier_currency": self.base_currency,
                "purchase_order_currency": self.target_currency,
                "currency_rate": self.exchange_rate,
                "items_subtotal": purchase_total_details.get("items_subtotal"),
                "domestic_shipping": purchase_total_details.get("domestic_shipping"),
                "domestic_total": purchase_total_details.get("domestic_total"),
                "service_fee": purchase_total_details.get("service_fee"),
                "service_fee_gst": purchase_total_details.get("service_fee_gst"),
                "service_fee_type": self.service_fee_type,
                "gst_percentage": self.gst_percentage,
                "purchase_commission_rate_percentage": self.commission_rate_percentage,
                "subtotal_before_international_shipping":
                    purchase_total_details.get("subtotal_before_international_shipping"),
                "estimated_international_shipping": purchase_total_details.get("estimated_international_shipping"),
                "promotion_applied": purchase_total_details.get("promotion_applied"),
                "logistics_provider_type": logistics_route_info.provider_type,
                "logistics_route_code": logistics_route_info.route_code,
                "logistics_route": logistics_route,
                "logistics_info": logistics_info,
                "delivery_address": delivery_address,
                "invoice_address": invoice_address,
                "order_total": order_total,
                "payment_currency": payment_currency,
                "payment_currency_rate": payment_currency_rate,
                "payment_amount": payment_amount
            }
        )

    async def covert_purchase_order_items(self, supply_products):
        log.info(f"supply_products is {supply_products}")
        order_items = []
        for product in supply_products:
            for variant in product.get("variants", []):
                variant_price_info = currency_utils.batch_convert_exchange_rate(
                    {
                        "actual_purchase_price": variant.get("price"),
                        "actual_purchase_total": variant.get("total")
                    }, self.exchange_rate
                )
                order_items.append(
                    jsonable_encoder(
                        {
                            "product_id": product.get("product_id"),
                            "product_name": product.get("product_name"),
                            "product_image": product.get("product_image"),
                            "seller_open_id": product.get("seller_open_id"),
                            "variant_id": variant.get("variant_id"),
                            "variant_name": variant.get("variant_name"),
                            "specific_id": variant.get("specific_id"),
                            "sku": variant.get("sku", ""),
                            "spu": variant.get("spu", ""),
                            "main_image_url": variant.get("variant_image"),
                            "purchase_commission_rate": self.commission_rate,
                            "quantity": variant.get("quantity"),
                            "currency": self.target_currency,
                            "weight": variant.get("parcel_info", {}).get("weight"),
                            **variant.get("parcel_info", {}).get("dimensions", {}),
                            "purchase_price": variant.get("price"),
                            "purchase_total": variant.get("total"),
                            "order_price": variant.get("order_price"),
                            "order_total": variant.get("order_total"),
                            **variant_price_info
                        }
                    )
                )
        return order_items

    async def create_supply_chain_sale_order(
            self, site_connection_id, supply_chain_variants, logistics_route_info, delivery_address,
            default_purchase_order_dict
    ):

        invoice_address = await self.get_invoice_address()
        supply_chain_products = await self.query_supply_chain_product_variants(
            site_connection_id, supply_chain_variants
        )
        logistics_fee, logistics_info, logistics_route = await self.compute_supply_chain_purchase_logistics_details(
            supply_chain_products, logistics_route_info, delivery_address
        )
        purchase_total_details = await self.get_supply_chain_purchase_shipping_total_dict(
            supply_chain_products, logistics_fee
        )
        purchase_order_dict = await self.covert_purchase_order(
            purchase_total_details, delivery_address, logistics_info, logistics_route_info, logistics_route,
            invoice_address
        )
        log.info(f"purchase_order_dict is {purchase_order_dict}")
        purchase_order_dict.update(default_purchase_order_dict)
        order_items = await self.covert_purchase_order_items(supply_chain_products)
        purchase_order_dict["order_lines"] = order_items
        purchase_order = await purchase_service.create_purchase_order_and_items(purchase_order_dict, order_items)
        return jsonable_encoder(purchase_order)

    async def create_supply_chain_purchase_order(self, site_connection_id, params: CreateSupplyChainPurchaseSchema):
        default_purchase_order_dict = {
            "purchase_order_type": PurchaseOrderTypeEnum.PURCHASE.value,
            "purchase_order_status": PurchaseOrderStatusEnum.PENDING.value
        }
        purchase_order = await self.create_supply_chain_sale_order(
            site_connection_id, params.variants, params.logistics_route_info, params.delivery_address,
            default_purchase_order_dict
        )
        return PurchaseOrderSchema(**purchase_order)

    async def get_shop_order_variants(self, wd_order_type, wd_order_id):
        shop_order, order_id, sub_order_id = await purchase_service.get_shop_order_info(wd_order_id, wd_order_type)
        target_site_connection_id = shop_order.site_connection_id
        source_site_connection_id, variants = await purchase_service.get_source_site_connection_product_variants(
            target_site_connection_id, order_id, sub_order_id
        )
        await self.reset_target_currency(shop_order.currency)
        return source_site_connection_id, variants, shop_order, order_id, sub_order_id

    async def get_shop_purchase_details(self, params: GetShopPurchaseOrderRequest):
        source_site_connection_id, variants, shop_order, order_id, sub_order_id = await self.get_shop_order_variants(
            params.order_type, params.order_id
        )
        supply_chain_variants = [
            GetSupplyChainVariantSchema(
                **{
                    "product_id": str(variant.product_id),
                    "variant_id": str(variant.variant_id),
                    "quantity": variant.quantity,
                    "order_price": variant.order_price
                }
            ) for variant in variants
        ]
        return await self.get_supply_chain_purchase_details(source_site_connection_id, supply_chain_variants)

    async def get_shop_purchase_total_details(self, params: GetShopPurchaseOrderRequest):
        source_site_connection_id, variants, shop_order, order_id, sub_order_id = await self.get_shop_order_variants(
            params.order_type, params.order_id
        )
        return await self.get_supply_chain_purchase_total_details(
            source_site_connection_id,
            GetSupplyChainPurchaseTotalDetailsRequest(
                **{
                    "variants": [
                        GetSupplyChainVariantSchema(
                            **{
                                "product_id": str(variant.product_id),
                                "variant_id": str(variant.variant_id),
                                "quantity": variant.quantity,
                                "order_price": variant.order_price
                            }
                        ) for variant in variants
                    ]}
            ))

    async def get_shop_logistics_details(self, params: GetShopPurchaseOrderRequest):
        source_site_connection_id, variants, shop_order, order_id, sub_order_id = await self.get_shop_order_variants(
            params.order_type, params.order_id
        )
        delivery_address = await self.convert_delivery_address(shop_order.shipping)
        return await self.get_logistics_details(
            source_site_connection_id,
            GetLogisticsDetailsRequest(
                **{
                    "delivery_address": delivery_address,
                    "variants": [
                        GetSupplyChainVariantSchema(
                            **{
                                "product_id": str(variant.product_id),
                                "variant_id": str(variant.variant_id),
                                "quantity": variant.quantity,
                                "order_price": variant.order_price
                            }
                        ) for variant in variants
                    ]}
            ))

    async def get_shop_purchase_shipping_total_details(self, params: GetShopPurchaseOrderRequest):
        source_site_connection_id, variants, shop_order, order_id, sub_order_id = await self.get_shop_order_variants(
            params.order_type, params.order_id
        )
        delivery_address = await self.convert_delivery_address(shop_order.shipping)
        return await self.get_supply_chain_purchase_shipping_total_details(
            source_site_connection_id,
            GetSupplyChainPurchaseShippingTotalDetailsRequest(
                **{
                    "logistics_route_info": params.logistics_route_info,
                    "delivery_address": delivery_address,
                    "variants": [
                        GetSupplyChainVariantSchema(
                            **{
                                "product_id": str(variant.product_id),
                                "variant_id": str(variant.variant_id),
                                "quantity": variant.quantity,
                                "order_price": variant.order_price
                            }
                        ) for variant in variants
                    ]}
            ))

    async def create_shop_purchase_order(self, params: CreateShopPurchaseOrderRequest, sale_user_id: int = None):
        source_site_connection_id, variants, shop_order, order_id, sub_order_id = await self.get_shop_order_variants(
            params.order_type, params.order_id
        )
        delivery_address = await self.convert_delivery_address(shop_order.shipping)
        default_purchase_order_dict = {
            "sale_user_id": sale_user_id,
            "wd_order_id": order_id,
            "wd_sub_order_id": sub_order_id,
            "wd_order_type": params.order_type,
            "wd_order_total": shop_order.total_price,
            "wd_order_currency": shop_order.currency,
            "site_info_id": shop_order.site_id,
            "client_order_ref": shop_order.order_id,
            "purchase_order_type": PurchaseOrderTypeEnum.DROP_SHIPPING.value,
            "purchase_order_status": PurchaseOrderStatusEnum.PENDING.value
        }
        variants = [
            GetSupplyChainVariantSchema(
                **{
                    "product_id": str(variant.product_id),
                    "variant_id": str(variant.variant_id),
                    "quantity": variant.quantity,
                    "order_price": variant.order_price
                }
            ) for variant in variants
        ]
        purchase_order = await self.create_supply_chain_sale_order(
            source_site_connection_id, variants, params.logistics_route_info, delivery_address,
            default_purchase_order_dict
        )
        wd_order_id = purchase_order.get("wd_order_id")
        wd_sub_order_id = purchase_order.get("wd_sub_order_id")
        await purchase_service.update_shop_order_status(
            wd_order_id, wd_sub_order_id, AIOrderStatus.PURCHASED.value
        )
        return PurchaseOrderSchema(**purchase_order)

    async def automated_purchase(self, target_site_connection_id, order_id, order_type, sale_user_id):
        source_site_connection_id, variants, shop_order, order_id, sub_order_id = await self.get_shop_order_variants(
            order_type, order_id
        )
        await self.init_supply_adapter(source_site_connection_id, self.org_id, self.token)
        if self.is_auto_drop_shipping and self.is_shipping_free and self.is_purchase_free:
            try:
                purchase_order = await self.create_shop_purchase_order(
                    CreateShopPurchaseOrderRequest(
                        **{
                            "order_id": order_id,
                            "order_type": order_type,
                            "logistics_route_info": CalcLogisticsRouteCostRequest(
                                **{
                                    "provider_type": "automatic",
                                    "route_code": "shortest_time",
                                    "parcel_info": {
                                        "weight": "0.00",
                                        "length": "0.00",
                                        "width": "0.00",
                                        "height": "0.00"
                                    }
                                }
                            )
                        }
                    ),
                    sale_user_id
                )
                purchase_order = await purchase_service.get_purchase_order_by_id(purchase_order.id)
                await self.purchase_pay_callback(purchase_order, ErpOrderPayStatusEnum.done.name)
            except Exception as e:
                log.error(e)
                raise e

    async def create_supply_chain_sub_purchase_orders(
            self, purchase_order, sale_orders
    ):
        if sale_orders:
            sub_purchase_orders = []
            for sale_order in sale_orders:
                fixed_service_fee = 0 if purchase_order.service_fee_type == "fixed" else purchase_order.service_fee
                purchase_total_details = sale_order.get("purchase_total_details")
                purchase_total_details = currency_utils.batch_convert_exchange_rate(
                    purchase_total_details, self.exchange_rate
                )
                sub_purchase_order_info = sale_order.get("purchase_order_info")
                purchase_total_details = await self.compute_supply_chain_purchase_total_details(
                    purchase_total_details, purchase_order.service_fee_type, fixed_service_fee,
                    decimal_utils.decimal_divide(
                        purchase_order.purchase_commission_rate_percentage, 100, 2
                    ),
                    purchase_order.gst_percentage
                )
                total = purchase_total_details.get("total")
                payment_currency_rate = purchase_order.payment_currency_rate
                payment_amount = decimal_utils.to_decimal(
                    decimal_utils.decimal_multiply(
                        total, payment_currency_rate, 3
                    ), precision=2
                )
                purchase_total_details.update({
                    "subtotal_before_international_shipping": total,
                    "order_total": total,
                    "payment_amount": payment_amount,
                    "supplier_order_status":
                        sub_purchase_order_info.get(
                            "supplier_order_status",
                            SupplyChainSaleOrderStatusEnum.pending.value
                        )
                })
                sub_purchase_order_dict = object_as_dict_advanced(purchase_order)
                sub_purchase_order_dict.pop("id")
                sub_purchase_order_dict.pop("created_at")
                sub_purchase_order_dict.pop("updated_at")
                sub_purchase_order_dict.update(purchase_total_details)
                sub_purchase_order_dict.update(sub_purchase_order_info)
                sub_purchase_orders.append(sub_purchase_order_dict)

            await purchase_service.create_purchase_sub_order(purchase_order, sub_purchase_orders)

    async def purchase_pay_callback(self, purchase_order, payment_status: str):
        await self.reset_target_currency(purchase_order.purchase_order_currency)
        await self.init_supply_adapter(purchase_order.source_site_connection_id, self.org_id, self.token)
        if payment_status == ErpOrderPayStatusEnum.done.name:
            await purchase_service.check_purchase_order_status(
                purchase_order.purchase_order_status, PurchaseOrderStatusEnum.PROCESSING.value
            )
            purchase_order.purchase_order_status = PurchaseOrderStatusEnum.PROCESSING.value
            sale_orders = await self.supply_adapter.create_sale_orders(purchase_order)
            await self.create_supply_chain_sub_purchase_orders(purchase_order, sale_orders)
            await purchase_service.purchase_pay_callback(purchase_order)

    async def cancel_supply_chain_sale_order(self, params: CancelSupplyChainSaleOrderRequest):
        purchase_sub_order = await purchase_service.get_purchase_sub_order_by_id(params.purchase_sub_order_id)
        await purchase_service.check_purchase_order_status(
            purchase_sub_order.purchase_order_status, PurchaseOrderStatusEnum.CANCELED.value
        )
        await self.init_supply_adapter(purchase_sub_order.source_site_connection_id, self.org_id, self.token)
        await self.supply_adapter.cancel_sale_order(
            purchase_sub_order.supplier_order_id, params.cancel_reason, params.remark
        )
        update_purchase_order_dict = {
            "id": purchase_sub_order.id,
            "purchase_order_status": PurchaseOrderStatusEnum.CANCELED.value,
            "cancel_reason": params.cancel_reason,
            "cancel_remark": params.remark
        }
        await purchase_service.update_purchase_sub_order(update_purchase_order_dict)


async def purchase_pay_callback(purchase_order_id: int, payment_status: str):
    log.info(f"purchase_order_id is {purchase_order_id}, payment_status is {payment_status}")
    purchase_order = await purchase_service.get_purchase_order_by_id(purchase_order_id)
    purchase_orchestrator = PurchaseOrchestrator(
        org_id=purchase_order.org_id, user_id=purchase_order.user_id, token="", common_headers=CommonHeaders(
            x_user_currency=purchase_order.purchase_order_currency
        )
    )
    await purchase_orchestrator.purchase_pay_callback(purchase_order, payment_status)


async def automated_purchase(target_site_connection_id, order_id, order_type):
    connection_info = get_site_connection_info(target_site_connection_id)
    if connection_info:
        org_id = connection_info.org_id
        # user_id = connection_info.user_id
        sale_user_id = connection_info.sale_user_id or connection_info.user_id
        await PurchaseOrchestrator(
            org_id, sale_user_id, "", common_headers=CommonHeaders(x_user_currency="AUD")
        ).automated_purchase(
            target_site_connection_id, order_id, order_type, sale_user_id
        )

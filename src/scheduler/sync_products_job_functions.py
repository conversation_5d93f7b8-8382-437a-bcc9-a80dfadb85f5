"""This file contains all job instances' main function"""

import pandas as pd

from db.pg_price_stock_settings import PGPriceStockSettings
from db.pg_products_sync import PGProductsSync
from db.pg_variants_sync import PGVariantsSync
from ecommerce import factory
from ecommerce.ecmarketplace import ECMarketplace
from ecommerce.ecshop import ECShop
from ecommerce.iscmarketplace import ISCMarketplace
from ecommerce.supplychain.isc_buyer_client import IscBuyerClient

from utility.log import get_logger

log = get_logger(__name__)


def sync_target_product(target_eshop, item):
    target_eshop.update_variant_price_and_stock(
        target_product_id=item.get("target_product_id"),
        target_variant_id=item.get("target_variant_id"),
        stock_quantity=item.get("stock_quantity"),
    )


def sync_price_only(target_eshop, item):
    target_eshop.update_variant_price_and_stock(
        target_product_id=item.get("target_product_id"),
        target_variant_id=item.get("target_variant_id"),
        price=item.get("source_price"),
    )


def sync_price_and_stock(target_eshop, item):
    target_eshop.update_variant_price_and_stock(
        target_product_id=item.get("target_product_id"),
        target_variant_id=item.get("target_variant_id"),
        stock_quantity=item.get("source_price"),
        price=item.get("source_price"),
    )


def fetch_variants_products(optional_conditions: dict) -> list[dict]:
    try:
        _optional_conditions = optional_conditions

        records = PGProductsSync.fetch_sync_products(optional_conditions=_optional_conditions)

        res = [
            {
                "wd_product_id": record[0].wd_product_id,
                "wd_variant_id": record[1].wd_variant_id,
                "source_product_id": record[0].source_product_id,
                "source_variant_id": record[1].source_variant_id,
                "target_product_id": record[0].target_product_id,
                "target_variant_id": record[1].target_variant_id,
            }
            for record in records
        ]
        return res
    except Exception as e:
        log.info(e)
        return []


async def products_sync_job(site_info_id: int, job_id: str):
    log.info(f"############ Launch auto sync job: {job_id}! ############")

    _optional_conditions = {"site_info_id": site_info_id}

    records = PGProductsSync.query_products_connection_ids(optional_conditions=_optional_conditions)

    if not records:
        log.log(f"Cannot find any records for site_info_id: {site_info_id}. Skip the cron job: {job_id}!")
        return

    for record in records:
        source_site_connection_id = record[0]
        target_site_connection_id = record[1]

        log.info(
            f"job source_site_connection_id: {source_site_connection_id} -"
            f"job target_site_connection_id: {target_site_connection_id}"
        )

        optional_conditions = {
            "site_info_id": site_info_id,
            "source_site_connection_id": source_site_connection_id,
            "target_site_connection_id": target_site_connection_id,
        }
        products = fetch_variants_products(optional_conditions=optional_conditions)

        log.info(f"fetch {len(products)} products")

        source_eshop: IscBuyerClient | ISCMarketplace = factory.get_eshop(
            site_connection_id=source_site_connection_id, token=None
        )

        target_eshop: ECMarketplace = factory.get_eshop(site_connection_id=target_site_connection_id, token=None)

        log.info("Calling valid_and_sync_products function ...")

        await valid_and_sync_products(
            products=products,
            site_info_id=site_info_id,
            source_eshop=source_eshop,
            target_eshop=target_eshop,
            is_scheduler=True,
        )

        log.info(f"############ Sync job for {job_id} is done! ############")
        return


def validate_source_products(
    products: list[dict],
    site_info_id: int,
    source_eshop: ISCMarketplace | IscBuyerClient,
    target_eshop: ECMarketplace | ECShop,
) -> list:
    missing_products_in_source = []
    valid_products_in_source = []
    try:
        log.info("Check upstream -- source products")
        for variant_product in products:
            res = source_eshop.get_variant_price_and_stock(
                source_product_id=variant_product.get("source_product_id"),
                source_variant_id=variant_product.get("source_variant_id"),
            )
            if not res:
                missing_products_in_source.append(variant_product)
            else:
                _variant_product = {
                    **variant_product,
                    "source_price": res.get("list_price"),
                    "source_stock": res.get("stock_quantity"),
                }
                valid_products_in_source.append(_variant_product)

        if missing_products_in_source:
            log.info(f"Found missing_products_in_source: {len(missing_products_in_source)}!")
            for missing_product in missing_products_in_source:
                # disable in the sync table
                PGProductsSync.update_synced_product(
                    update_detail={"is_fully_synced": False},
                    site_info_id=site_info_id,
                    source_site_connection_id=source_eshop.site_connection_id,
                    target_site_connection_id=target_eshop.site_connection_id,
                    source_product_id=missing_product.get("source_product_id"),
                    target_product_id=missing_product.get("target_product_id"),
                )

                PGVariantsSync.update_synced_variant(
                    update_detail={"variant_availability": False, "is_sync_variant": False},
                    source_variant_id="source_variant_id",
                    target_variant_id="target_variant_id",
                )

                # Set downstream site stock as zero
                target_eshop.update_variant_price_and_stock(
                    target_product_id=missing_product.get("target_product_id"),
                    target_variant_id=missing_product.get("target_variant_id"),
                    stock_quantity=0,
                )
        return valid_products_in_source

    except Exception as e:
        log.error(e)
        return valid_products_in_source


def validate_target_products(
    valid_products_in_source: list[dict],
    site_info_id: int,
    source_eshop: ISCMarketplace | IscBuyerClient,
    target_eshop: ECMarketplace | ECShop,
) -> list:
    missing_products_in_target = []
    valid_products_in_target = []
    valid_products = []
    try:
        log.info("Check downstream -- target products")
        for variant_product in valid_products_in_source:
            res = target_eshop.exist_variant(
                target_product_id=variant_product.get("target_product_id"),
                target_variant_id=variant_product.get("target_variant_id"),
            )

            if not res:
                missing_products_in_target.append(variant_product)
            else:
                valid_products_in_target.append(variant_product)

        if missing_products_in_target:
            log.info(f"Found missing_products_in_target: {len(missing_products_in_target)}!")
            for missing_product in missing_products_in_target:
                # disable in the sync table
                PGProductsSync.update_synced_product(
                    update_detail={"is_fully_synced": False},
                    site_info_id=site_info_id,
                    source_site_connection_id=source_eshop.site_connection_id,
                    target_site_connection_id=target_eshop.site_connection_id,
                    source_product_id=missing_product.get("source_product_id"),
                    target_product_id=missing_product.get("target_product_id"),
                )

                PGVariantsSync.update_synced_variant(
                    update_detail={"variant_availability": False, "is_sync_variant": False},
                    source_variant_id="source_variant_id",
                    target_variant_id="target_variant_id",
                )

        # Add price and stock info in target side
        for variant_product in valid_products_in_target:
            res = target_eshop.get_variant_price_and_stock(
                target_product_id=variant_product.get("target_product_id"),
                target_variant_id=variant_product.get("target_variant_id"),
            )
            if res:
                _variant_product = {
                    **variant_product,
                    "target_price": res.get("price"),
                    "target_stock": res.get("stock_quantity"),
                }
                valid_products.append(_variant_product)
            else:
                _target_product_id = variant_product.get("target_product_id")
                _target_product_id = variant_product.get("target_product_id")
                log.warning(
                    f"We should not get empty for the " f"product: {_target_product_id} - variant: {_target_product_id}"
                )

        return valid_products

    except Exception as e:
        log.error(e)
        return valid_products


async def sync_valid_products(
    valid_products: list[dict], site_info_id: int, target_eshop: ECMarketplace, is_scheduler: bool
):
    log.info(f"site_info_id: {site_info_id}")
    df_all_variants = pd.DataFrame(valid_products)
    df_all_variants["source_stock"] = df_all_variants["source_stock"].astype(int)
    df_all_variants["target_stock"] = df_all_variants["target_stock"].astype(int)
    df_all_variants["source_price"] = df_all_variants["source_price"].apply(lambda x: f"{x:.2f}")
    df_all_variants["target_price"] = df_all_variants["target_price"].apply(lambda x: f"{x:.2f}")
    # df_all_variants["target_price"] = df_all_variants["target_price"].astype(float).round(2)

    site_info_id = target_eshop.site_info_id
    sync_price_and_stock_setting = PGPriceStockSettings.get_record(site_info_id=site_info_id)

    sync_price = sync_price_and_stock_setting.get("price_status")
    sync_stock = sync_price_and_stock_setting.get("stock_status")

    if is_scheduler:
        log.info("Detect the scheduler job, set sync_price only as True!")
        log.info("Detect the scheduler job, set sync_price only as True!")
        sync_price = False
        sync_stock = True

    df_sync_variants = None
    if sync_price and sync_stock:
        df_sync_variants = df_all_variants[
            (df_all_variants["source_price"] != df_all_variants["target_price"])
            | (df_all_variants["source_stock"] != df_all_variants["target_stock"])
        ]

        if df_sync_variants.shape[0] == 0:
            log.info("Nothing different -- price & stock between source and target!")
        else:
            log.info(f"{df_sync_variants.shape[0]} products will need to be synced!")
            df_sync_variants.apply(lambda row: sync_price_and_stock(target_eshop, row), axis=1)

    elif sync_price:
        df_sync_variants = df_all_variants[df_all_variants["source_price"] != df_all_variants["target_price"]]

        if df_sync_variants.shape[0] == 0:
            log.info("Nothing different -- price between source and target!")
        else:
            log.info(f"{df_sync_variants.shape[0]} products will need to be synced!")
            df_sync_variants.apply(lambda row: sync_price_only(target_eshop, row), axis=1)

    elif sync_stock:
        df_sync_variants = df_all_variants[df_all_variants["source_stock"] != df_all_variants["target_stock"]]

        if df_sync_variants.shape[0] == 0:
            log.info("Nothing different -- stock between source and target!")
            log.info("Nothing different -- stock between source and target!")
        else:
            log.info(f"{df_sync_variants.shape[0]} products will need to be synced!")
            log.info(f"{df_sync_variants.shape[0]} products will need to be synced!")
            df_sync_variants.apply(lambda row: sync_target_product(target_eshop, row), axis=1)

    log.info(f"the variants number need to sync update: {df_sync_variants.shape[0]}")
    log.info(f"the variants number need to sync update: {df_sync_variants.shape[0]}")
    return


async def run_sync_all_products(conditions: dict, source_eshop: ISCMarketplace, target_eshop: ECMarketplace):
    # We split the data in the table into small chunk, and each chunk has page_size records
    page = 1
    page_size = 100
    conditions = conditions if conditions else {}

    while True:
        try:
            products = PGProductsSync.fetch_paginated_products(
                page=page, page_size=page_size, optional_conditions=conditions
            )
            if not products:
                break

            await sync_all_products(
                products=products,
                site_info_id=conditions.get("site_info_id"),
                source_eshop=source_eshop,
                target_eshop=target_eshop,
            )
        except Exception as e:
            log.error(e)
            break


async def valid_and_sync_products(
    products: list[dict],
    site_info_id: int,
    source_eshop: ISCMarketplace | IscBuyerClient,
    target_eshop: ECMarketplace | ECShop,
    is_scheduler: bool = False,
):
    # Step 1 -- validate source end products status before sync
    log.info("Step 1 -- validate source end products status before sync")
    log.info("Step 1 -- validate source end products status before sync")
    valid_products_in_source = validate_source_products(
        products=products, site_info_id=site_info_id, source_eshop=source_eshop, target_eshop=target_eshop
    )

    # Step 2 -- validate target end products status before sync
    log.info("Step 2 -- validate target end products status before sync")
    log.info("Step 2 -- validate target end products status before sync")
    valid_products = validate_target_products(
        valid_products_in_source=valid_products_in_source,
        site_info_id=site_info_id,
        source_eshop=source_eshop,
        target_eshop=target_eshop,
    )

    # Step 3 -- compare the difference of price and stock between source and target end, then, sync
    log.info("Step 3 -- compare the difference of price and stock between source and target end, then, sync")
    log.info("Step 3 -- compare the difference of price and stock between source and target end, then, sync")
    await sync_valid_products(
        valid_products=valid_products, site_info_id=site_info_id, target_eshop=target_eshop, is_scheduler=is_scheduler
    )

    log.info("sync_all_products process is finished.")
    log.info("sync_all_products process is finished.")
    return


async def sync_all_products(
    variants_ids: list[int],
    site_info_id: int,
    source_eshop: ISCMarketplace | IscBuyerClient,
    target_eshop: ECMarketplace | ECShop,
):
    """
    Before sync, we check 2 sides:
    Upstream (source_eshop) check - the product does exist in ISC end, otherwise, downstream sync stock as 0, sync table
    disable availability and is_sync flags.

    Downstream (target_eshop) check - the product does exist in Site end, otherwise, sync table
    disable availability flags.

    Only when we satisfy both condition, then sync stock or/and price.
    """
    log.info("Checking [SOURCE CLIENT] product status via product ids....")
    # Step 0 -- Get all data we need from 2 tables join:
    # ProductsSyncTable & VariantsSyncTable
    optional_conditions = {"variant_sync_ids": variants_ids}
    products = fetch_variants_products(optional_conditions=optional_conditions)

    await valid_and_sync_products(
        products=products, site_info_id=site_info_id, source_eshop=source_eshop, target_eshop=target_eshop
    )

    return

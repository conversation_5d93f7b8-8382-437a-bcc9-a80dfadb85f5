from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend
from fastapi_cache.decorator import cache
from fastapi_versioning import VersionedFastAPI
from redis import asyncio as aioredis
from requests import Request
from starlette.middleware.cors import CORSMiddleware
from starlette.responses import JSONResponse

from config_params.config_params_endpoint import router_config_params
from ecommerce.logistics.logistics_endpoint import router_logistics
from ecommerce.order.order_endpoint import router_order
from ecommerce.order.webhook_endpoint import router_order_webhook
from ecommerce.product.product_endpoint import router_product
from ecommerce.product.recommender_endpoint import router_recommender
from ecommerce.product.sync_endpoint import router_sync
from ecommerce.product.webhook_endpoint import router_product_webhook
from ecommerce.purchase.purchase_endpoint import router_purchase
from posthog.endpoint_posthog import router_posthog
from products.products_endpoint import router_rec_product
from redis_queue.redis_queue_endpoint import router_redis_queue
from supplier.affiliates.alibaba.endpoint_alibaba_category import router_alibaba_category
from supplier.affiliates.alibaba.endpoint_alibaba_order import router_alibaba_order
from supplier.affiliates.alibaba.endpoint_alibaba_product import router_alibaba_product
from supplier.affiliates.eshop.endpoint_sync_product import router_supplier_product
from supplier.affiliates.isc.endpoint_isc_product import router_supplier_isc
from utility.config import REDIS_HOST, REDIS_PORT
from utility.exception_handler import BusinessException
from utility.log import get_logger

log = get_logger(__name__)

tags_metadata = [
    {
        "name": "Products init",
        "description": "init website products data with specific **product ids** or all products",
    },
    {
        "name": "Webhooks",
        "description": "Webhook APIs for product changing capture. "
                       "Called by eCommerce Platform. Refer to eCommerce Platform Webhook API docs.",
        "externalDocs": {
            "description": "Woocommerce REST API webhooks external docs",
            "url": "http://woocommerce.github.io/woocommerce-rest-api-docs/#webhooks",
        },
    },
]


def start():
    pass


def end():
    pass


@asynccontextmanager
async def lifespan(_: FastAPI):
    """
    This is FastAPI APP lifecycle function, and you can hook your logic at beginning and ending of the App server.
    https://fastapi.tiangolo.com/advanced/events/
    The first part of the function, before the yield, will be executed before the application starts.
    And the part after the yield will be executed after the application has finished.
    """
    log.info("########### API is launching !!! ###########")
    redis = aioredis.Redis(host=REDIS_HOST, port=REDIS_PORT, db=2)
    FastAPICache.init(RedisBackend(redis), prefix="fastapi-cache")
    log.info("start fastapi cache")

    # cronjob scheduler
    # log.info("########### Launch scheduler cronjob !!! ###########")
    # scheduler_instance = Scheduler(name="Nuwa_products_sync_cronjob")
    # await scheduler_instance.launch_scheduler()

    yield
    # log.info("########### scheduler is terminated !!! ###########")
    # scheduler_instance.shutdown()


nuwa_router = FastAPI(
    title="Warp Driven Nuwa (女娲) Platform API",
    description="Welcome to Warp Driven Nuwa Platform API docs",
    version="1.0.0",
    openapi_tags=tags_metadata,
)

# Register routers by functional domain
def register_routers(_app: FastAPI) -> None:
    """Register all application routers organized by functional domain."""

    # Core eCommerce routers
    ecommerce_routers = [
        router_product,
        router_order,
        router_purchase,
        router_logistics,
        router_sync,
        router_recommender,
    ]

    # Webhook routers for external integrations
    webhook_routers = [
        router_product_webhook,
        router_order_webhook,
    ]

    # Supplier and affiliate routers
    supplier_routers = [
        router_alibaba_category,
        router_alibaba_product,
        router_alibaba_order,
        router_supplier_product,
        router_supplier_isc,
    ]

    # Product and recommendation routers
    product_routers = [
        router_rec_product,
    ]

    # Infrastructure and utility routers
    infrastructure_routers = [
        router_redis_queue,
        router_posthog,
        router_config_params,
    ]

    # Register all router groups
    for router_group in [
        ecommerce_routers,
        webhook_routers,
        supplier_routers,
        product_routers,
        infrastructure_routers,
    ]:
        for router in router_group:
            _app.include_router(router)


# Register all routers
register_routers(nuwa_router)

app = VersionedFastAPI(nuwa_router, default_api_version=(1, 0), enable_latest=True, lifespan=lifespan)


# Mount the static directory
# app.mount("/static", StaticFiles(directory="static"), name="static")


# @app.on_event("startup")
# def startup_hook():
#     print("API is launching !!!")
#
#
# @app.on_event("shutdown")
# def shutdown_hook():
#     print("API is terminated !!!")


@app.get("/")
def read_root():
    return {"message": "Hello, WarpDriven Nuwa!"}


@app.get("/cache")
@cache()
def get_cache():
    return {"cache": True}

@app.middleware("http")
async def business_exception_middleware(request: Request, call_next):
    """
    Return consistent error format for all exceptions.
    """
    try:
        return await call_next(request)
    except BusinessException as e:
        # Business 类异常
        return JSONResponse(
            status_code=e.status_code,
            content=e.detail
        )
    except HTTPException as e:
        # FastAPI 内置异常
        return JSONResponse(
            status_code=e.status_code,
            content={"message": e.detail, "exception": str(e.detail)}
        )
    except Exception as e:
        # 未知异常
        return JSONResponse(
            status_code=500,
            content={"message": "Internal Server Error", "exception": str(e)}
        )

# @nuwa_router.middleware("http")
# async def log_requests(request: Request, call_next):
#     log.info(f"Received request {request.method} {request.url}. Headers: {request.headers}")
#     response = await call_next(request)
#     return response


# origins = ["https
#
# ://woolworlds.com"]
# origins_reg = "https://.*\.warp-driven\.com"
# allow_origin_regex=origins_reg,

# !!! Remember: to add the add_middleware to the direct app not other 2nd router.
origins = ["*"]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

if __name__ == "__main__":
    uvicorn.run(app=app, host="0.0.0.0", port=3000)
